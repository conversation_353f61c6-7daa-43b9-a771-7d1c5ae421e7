{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import matplotlib as mpl\n", "import pickle\n", "from itertools import product\n", "from scipy.stats import spearmanr, pearsonr\n", "from sklearn.linear_model import LinearRegression, Ridge\n", "import logomaker\n", "\n", "#from Bio.Seq import Seq\n", "from sklearn.decomposition import PCA\n", "from sklearn.manifold import TSNE\n", "#from seqtools import *\n", "\n", "font = {'family' : 'sans-serif',\n", "        'size'   : 16}\n", "mpl.rc('font', **font)\n", "mpl.rc('lines', linewidth=2)\n", "mpl.rcParams['axes.linewidth'] = 2\n", "mpl.rcParams['xtick.major.width'] = 2\n", "mpl.rcParams['ytick.major.width'] = 2\n", "\n", "ALL_AAS = (\"A\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"K\", \"L\", \"M\", \"N\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"V\", \"W\", \"Y\")\n", "\n", "codon2protein_ = {'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L', 'TCT': 'S', 'TCC': 'S', 'TCA': 'S',\n", "                      'TCG': 'S', 'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*', 'TGT': 'C', 'TGC': 'C',\n", "                      'TGA': '*', 'TGG': 'W', 'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L', 'CCT': 'P',\n", "                      'CCC': 'P', 'CCA': 'P', 'CCG': 'P', 'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',\n", "                      'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R', 'ATT': 'I', 'ATC': 'I', 'ATA': 'I',\n", "                      'ATG': 'M', 'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T', 'AAT': 'N', 'AAC': 'N',\n", "                      'AAA': 'K', 'AAG': 'K', 'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R', 'GTT': 'V',\n", "                      'GTC': 'V', 'GTA': 'V', 'GTG': 'V', 'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',\n", "                      'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E', 'GGT': 'G', 'GGC': 'G', 'GGA': 'G',\n", "                      'GGG': 'G'}\n", "\n", "def get_protein_seq(seq):\n", "    if seq == '#PARENT#':\n", "        return 'WYLQF'\n", "    else:\n", "        protein_seq = ''\n", "        for i in range(0, len(seq), 3):\n", "            codon = seq[i:i+3]\n", "            protein_seq += codon2protein_[codon]\n", "        return protein_seq\n", "\n", "def shorten_seq(seq):\n", "    if seq == '#PARENT#':\n", "        return '#PARENT#'\n", "    else:\n", "        return seq[55*3:57*3] + seq[58*3:60*3] + seq[88*3:89*3]\n", "\n", "def variant2codons(variant):    \n", "    seq = 'TGGTACCTGCAGTTC'\n", "    if variant == '#PARENT#':\n", "        return seq\n", "    else:\n", "        dict = {166: 0, 167: 1, 168: 2, 169:3, 170:4, 171:5, 175:6, 176:7, 177:8, 178:9, 179:10, 180:11, 265:12, 266: 13, 267:14}\n", "        split = variant.split('_')\n", "        positions = []\n", "        for mut in split:\n", "            position = int(mut[1:-1])\n", "            if position < 166:\n", "                print('issue')\n", "            elif position > 180 and position < 265:\n", "                print('issue')\n", "            elif position not in dict.keys(): #if it's outside of the range\n", "                pass\n", "            else:\n", "                index = dict[position]\n", "                seq = seq[:index] + mut[-1] + seq[index+1:]\n", "        return seq\n", "\n", "        \n", "\n", "#check for deletion before 89*3\n", "def check_deletion(variant):\n", "    if variant == '#PARENT#':\n", "        return True\n", "    else:\n", "        split = variant.split('_')\n", "        values = []\n", "        #print(split)\n", "        for mut in split:\n", "            if 'DEL' in mut:\n", "                values.append(int(mut[1:-3]))\n", "        if len(values) == 0:\n", "            #print('no deletion')\n", "            return True\n", "        else:\n", "            min = np.min(values)\n", "            #print('deletion')\n", "            return min > 89*3\n", "\n", "def reformat(x):\n", "    if (len(x) == 2):\n", "        x = x[0] + '0' + x[1]\n", "    return x\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["#cleaned_df = merged[merged['VariantsFound'] == 5]\n", "#manually drop well D06\n", "#cleaned_df = cleaned_df.drop(cleaned_df[cleaned_df['Well'] == 'D06'].index)\n", "\n", "def process(cleaned_df, activity_col):\n", "    #cleaned_df = cleaned_df[cleaned_df['WellSeqDepth'] > 10]\n", "    #cleaned_df = cleaned_df[cleaned_df['AlignmentFrequency'] > 0.7]\n", "\n", "\n", "    #there's a simpler way to normalize to parents on the plate but i forgot how\n", "    # for plate in [1, 2, 3, 4]:\n", "    #     plate_df =  cleaned_df[cleaned_df['Plate'] == plate]\n", "    #     mean = plate_df[plate_df['SimpleCombo'] == 'WYLQF'][activity_col].mean()\n", "    #     norm_std = plate_df[plate_df['SimpleCombo'] == 'WYLQF'][activity_col].std()/mean\n", "    #     #print(norm_std)\n", "    #     #divide rows in cleaned_df by mean for that plate\n", "    #     cleaned_df.loc[cleaned_df['Plate'] == plate, activity_col] = cleaned_df.loc[cleaned_df['Plate'] == plate, activity_col]/mean\n", "\n", "    expanded_df = cleaned_df.copy()\n", "\n", "    indices = cleaned_df[cleaned_df['Combo'] == 'WYLQF'][~cleaned_df['Well'].isin(['B04', 'C05', 'D06', 'E07', 'F08', 'G09'])].index\n", "    cleaned_df = cleaned_df.drop(indices)\n", "    #parent_df = cleaned_df[cleaned_df['Combo'] == 'WYLQF'][cleaned_df['Well'].isin(['B04', 'C05', 'D06', 'E07', 'F08', 'G09'])]\n", "    expanded_df['Control'] = expanded_df['Well'].isin(['B04', 'C05', 'D06', 'E07', 'F08', 'G09'])\n", "    \n", "    #cleaned_df = cleaned_df[cleaned_df['Plate'] == 4]\n", "    counts_df = cleaned_df.groupby(['Combo']).size().reset_index()\n", "    cleaned_df = cleaned_df.groupby(['Combo']).mean()\n", "    cleaned_df['num_wells'] = counts_df.iloc[:, 1].values\n", "    cleaned_df = cleaned_df.reset_index().drop('Plate', axis=1)\n", "    cleaned_df[activity_col] = cleaned_df[activity_col]\n", "    #cleaned_df['Fitness'] = cleaned_df['Integration ']/cleaned_df['Integration '].max()\n", "\n", "    #take all rows with Combo containing *\n", "    stop_df = cleaned_df[cleaned_df['Combo'].str.contains('\\*')]\n", "    mean = stop_df[activity_col].mean()\n", "    std = stop_df[activity_col].std()\n", "    print(mean, std)\n", "    print(stop_df[activity_col].max())\n", "\n", "    cleaned_df = cleaned_df[~cleaned_df['Combo'].str.contains('\\*')]\n", "\n", "    cleaned_df = cleaned_df.sort_values(by=[activity_col], ascending=False)\n", "    return stop_df, expanded_df, cleaned_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### For processing <PERSON><PERSON><PERSON>'s output"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n", "issue\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>Variant</th>\n", "      <th>Alignment Probability</th>\n", "      <th>Alignment Count</th>\n", "      <th>Row</th>\n", "      <th>Column</th>\n", "      <th>Well</th>\n", "      <th>Plate</th>\n", "      <th>CodonCombo</th>\n", "      <th><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>263</td>\n", "      <td>T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...</td>\n", "      <td>0.798821</td>\n", "      <td>248.0</td>\n", "      <td>A</td>\n", "      <td>1</td>\n", "      <td>A01</td>\n", "      <td>1</td>\n", "      <td>ACTAATATGCCTGAG</td>\n", "      <td>TNMPE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>262</td>\n", "      <td>T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...</td>\n", "      <td>0.717116</td>\n", "      <td>300.0</td>\n", "      <td>A</td>\n", "      <td>2</td>\n", "      <td>A02</td>\n", "      <td>1</td>\n", "      <td>ACTAATATGCCTGAG</td>\n", "      <td>TNMPE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>186</td>\n", "      <td>G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...</td>\n", "      <td>0.572425</td>\n", "      <td>410.0</td>\n", "      <td>A</td>\n", "      <td>3</td>\n", "      <td>A03</td>\n", "      <td>1</td>\n", "      <td>TCTATGAGTCATTGT</td>\n", "      <td>SMSHC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>190</td>\n", "      <td>T166G_G167T_A170G_C171T_C175A_T176G_C178G_A179...</td>\n", "      <td>0.476841</td>\n", "      <td>297.0</td>\n", "      <td>A</td>\n", "      <td>4</td>\n", "      <td>A04</td>\n", "      <td>1</td>\n", "      <td>GTGTGTAGGGCGTCG</td>\n", "      <td>VCRAS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>277</td>\n", "      <td>T166A_G168T_T169A_A170T_C171T_C175G_G177T_C178...</td>\n", "      <td>0.610185</td>\n", "      <td>424.0</td>\n", "      <td>A</td>\n", "      <td>5</td>\n", "      <td>A05</td>\n", "      <td>1</td>\n", "      <td>AGTATTGTTAAGACG</td>\n", "      <td>SIVKT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>369</th>\n", "      <td>129</td>\n", "      <td>T166A_G167C_A170C_C171T_C175G_T176G_G177T_C178...</td>\n", "      <td>0.235028</td>\n", "      <td>156.0</td>\n", "      <td>H</td>\n", "      <td>8</td>\n", "      <td>H08</td>\n", "      <td>4</td>\n", "      <td>ACGTCTGGTATGTGT</td>\n", "      <td>TSGMC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>120</td>\n", "      <td>T166A_G167T_A170G_C171G_T176G_C178A_A179C_C267...</td>\n", "      <td>0.363358</td>\n", "      <td>455.0</td>\n", "      <td>H</td>\n", "      <td>9</td>\n", "      <td>H09</td>\n", "      <td>4</td>\n", "      <td>ATGTGGCGGACGTTT</td>\n", "      <td>MWRTF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>371</th>\n", "      <td>163</td>\n", "      <td>G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...</td>\n", "      <td>0.545897</td>\n", "      <td>270.0</td>\n", "      <td>H</td>\n", "      <td>10</td>\n", "      <td>H10</td>\n", "      <td>4</td>\n", "      <td>TCTATGAGTCATACT</td>\n", "      <td>SMSHT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>372</th>\n", "      <td>136</td>\n", "      <td>T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...</td>\n", "      <td>0.783072</td>\n", "      <td>208.0</td>\n", "      <td>H</td>\n", "      <td>11</td>\n", "      <td>H11</td>\n", "      <td>4</td>\n", "      <td>ACTAATATGCCTCAG</td>\n", "      <td>TNMPQ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>373</th>\n", "      <td>147</td>\n", "      <td>T166C_G167C_T169A_A170C_C171T_C175G_T176G_C178...</td>\n", "      <td>0.450191</td>\n", "      <td>136.0</td>\n", "      <td>H</td>\n", "      <td>12</td>\n", "      <td>H12</td>\n", "      <td>4</td>\n", "      <td>CCGACTGGGGAGTAG</td>\n", "      <td>PTGE*</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>343 rows × 10 columns</p>\n", "</div>"], "text/plain": ["     Unnamed: 0                                            Variant  \\\n", "0           263  T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...   \n", "1           262  T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...   \n", "2           186  G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...   \n", "3           190  T166G_G167T_A170G_C171T_C175A_T176G_C178G_A179...   \n", "4           277  T166A_G168T_T169A_A170T_C171T_C175G_G177T_C178...   \n", "..          ...                                                ...   \n", "369         129  T166A_G167C_A170C_C171T_C175G_T176G_G177T_C178...   \n", "370         120  T166A_G167T_A170G_C171G_T176G_C178A_A179C_C267...   \n", "371         163  G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...   \n", "372         136  T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...   \n", "373         147  T166C_G167C_T169A_A170C_C171T_C175G_T176G_C178...   \n", "\n", "     Alignment Probability  Alignment Count Row  Column Well  Plate  \\\n", "0                 0.798821            248.0   A       1  A01      1   \n", "1                 0.717116            300.0   A       2  A02      1   \n", "2                 0.572425            410.0   A       3  A03      1   \n", "3                 0.476841            297.0   A       4  A04      1   \n", "4                 0.610185            424.0   A       5  A05      1   \n", "..                     ...              ...  ..     ...  ...    ...   \n", "369               0.235028            156.0   H       8  H08      4   \n", "370               0.363358            455.0   H       9  H09      4   \n", "371               0.545897            270.0   H      10  H10      4   \n", "372               0.783072            208.0   H      11  H11      4   \n", "373               0.450191            136.0   H      12  H12      4   \n", "\n", "          CodonCombo  Combo  \n", "0    ACTAATATGCCTGAG  TNMPE  \n", "1    ACTAATATGCCTGAG  TNMPE  \n", "2    TCTATGAGTCATTGT  SMSHC  \n", "3    GTGTGTAGGGCGTCG  VCRAS  \n", "4    AGTATTGTTAAGACG  SIVKT  \n", "..               ...    ...  \n", "369  ACGTCTGGTATGTGT  TSGMC  \n", "370  ATGTGGCGGACGTTT  MWRTF  \n", "371  TCTATGAGTCATACT  SMSHT  \n", "372  ACTAATATGCCTCAG  TNMPQ  \n", "373  CCGACTGGGGAGTAG  PTGE*  \n", "\n", "[343 rows x 10 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["seq_df2 = pd.read_csv('initial/ParPgb_minION2.csv')\n", "seq_df2['Well'] = seq_df2['FBC']\n", "seq_df2['Plate'] = seq_df2['RBC'].apply(lambda x: int(x[-2:])- 4)\n", "seq_df2 = seq_df2[seq_df2['Plate'] < 5] #first four plates are 89X, not sure what the rest are\n", "seq_df2.dropna(subset=['Variant'], inplace=True)\n", "seq_df2 = seq_df2[seq_df2['Variant'].apply(check_deletion)]\n", "seq_df2['CodonCombo'] = seq_df2['Variant'].apply(variant2codons)\n", "\n", "#seq_df2['SimpleCombo_MinION'] = seq_df2['CodonCombo'].apply(get_protein_seq)\n", "seq_df2['Combo'] = seq_df2['CodonCombo'].apply(get_protein_seq)\n", "seq_df2.drop(['Position', 'RBC', 'FBC'], axis=1, inplace=True)\n", "seq_df2\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3747626/235143230.py:1: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  parent_df = seq_df2[seq_df2['Combo'] == 'WYLQF'][seq_df2['Well'].isin(['B04', 'C05', 'D06', 'E07', 'F08', 'G09'])]\n"]}], "source": ["parent_df = seq_df2[seq_df2['Combo'] == 'WYLQF'][seq_df2['Well'].isin(['B04', 'C05', 'D06', 'E07', 'F08', 'G09'])]\n", "#parent_df.to_csv('parents_sanger.csv', index=False)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# index = parent_df.index\n", "# #remove these indices from seq_df2\n", "# seq_df2.drop(index, inplace=True)\n", "# seq_df2"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Now process fitness"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>Variant</th>\n", "      <th>Alignment Probability</th>\n", "      <th>Alignment Count</th>\n", "      <th>Row</th>\n", "      <th>Column</th>\n", "      <th>Well</th>\n", "      <th>Plate</th>\n", "      <th>CodonCombo</th>\n", "      <th><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>263</td>\n", "      <td>T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...</td>\n", "      <td>0.798821</td>\n", "      <td>248.0</td>\n", "      <td>A</td>\n", "      <td>1</td>\n", "      <td>A01</td>\n", "      <td>1</td>\n", "      <td>ACTAATATGCCTGAG</td>\n", "      <td>TNMPE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>262</td>\n", "      <td>T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...</td>\n", "      <td>0.717116</td>\n", "      <td>300.0</td>\n", "      <td>A</td>\n", "      <td>2</td>\n", "      <td>A02</td>\n", "      <td>1</td>\n", "      <td>ACTAATATGCCTGAG</td>\n", "      <td>TNMPE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>186</td>\n", "      <td>G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...</td>\n", "      <td>0.572425</td>\n", "      <td>410.0</td>\n", "      <td>A</td>\n", "      <td>3</td>\n", "      <td>A03</td>\n", "      <td>1</td>\n", "      <td>TCTATGAGTCATTGT</td>\n", "      <td>SMSHC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>190</td>\n", "      <td>T166G_G167T_A170G_C171T_C175A_T176G_C178G_A179...</td>\n", "      <td>0.476841</td>\n", "      <td>297.0</td>\n", "      <td>A</td>\n", "      <td>4</td>\n", "      <td>A04</td>\n", "      <td>1</td>\n", "      <td>GTGTGTAGGGCGTCG</td>\n", "      <td>VCRAS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>277</td>\n", "      <td>T166A_G168T_T169A_A170T_C171T_C175G_G177T_C178...</td>\n", "      <td>0.610185</td>\n", "      <td>424.0</td>\n", "      <td>A</td>\n", "      <td>5</td>\n", "      <td>A05</td>\n", "      <td>1</td>\n", "      <td>AGTATTGTTAAGACG</td>\n", "      <td>SIVKT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>369</th>\n", "      <td>129</td>\n", "      <td>T166A_G167C_A170C_C171T_C175G_T176G_G177T_C178...</td>\n", "      <td>0.235028</td>\n", "      <td>156.0</td>\n", "      <td>H</td>\n", "      <td>8</td>\n", "      <td>H08</td>\n", "      <td>4</td>\n", "      <td>ACGTCTGGTATGTGT</td>\n", "      <td>TSGMC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>120</td>\n", "      <td>T166A_G167T_A170G_C171G_T176G_C178A_A179C_C267...</td>\n", "      <td>0.363358</td>\n", "      <td>455.0</td>\n", "      <td>H</td>\n", "      <td>9</td>\n", "      <td>H09</td>\n", "      <td>4</td>\n", "      <td>ATGTGGCGGACGTTT</td>\n", "      <td>MWRTF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>371</th>\n", "      <td>163</td>\n", "      <td>G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...</td>\n", "      <td>0.545897</td>\n", "      <td>270.0</td>\n", "      <td>H</td>\n", "      <td>10</td>\n", "      <td>H10</td>\n", "      <td>4</td>\n", "      <td>TCTATGAGTCATACT</td>\n", "      <td>SMSHT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>372</th>\n", "      <td>136</td>\n", "      <td>T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...</td>\n", "      <td>0.783072</td>\n", "      <td>208.0</td>\n", "      <td>H</td>\n", "      <td>11</td>\n", "      <td>H11</td>\n", "      <td>4</td>\n", "      <td>ACTAATATGCCTCAG</td>\n", "      <td>TNMPQ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>373</th>\n", "      <td>147</td>\n", "      <td>T166C_G167C_T169A_A170C_C171T_C175G_T176G_C178...</td>\n", "      <td>0.450191</td>\n", "      <td>136.0</td>\n", "      <td>H</td>\n", "      <td>12</td>\n", "      <td>H12</td>\n", "      <td>4</td>\n", "      <td>CCGACTGGGGAGTAG</td>\n", "      <td>PTGE*</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>343 rows × 10 columns</p>\n", "</div>"], "text/plain": ["     Unnamed: 0                                            Variant  \\\n", "0           263  T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...   \n", "1           262  T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...   \n", "2           186  G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...   \n", "3           190  T166G_G167T_A170G_C171T_C175A_T176G_C178G_A179...   \n", "4           277  T166A_G168T_T169A_A170T_C171T_C175G_G177T_C178...   \n", "..          ...                                                ...   \n", "369         129  T166A_G167C_A170C_C171T_C175G_T176G_G177T_C178...   \n", "370         120  T166A_G167T_A170G_C171G_T176G_C178A_A179C_C267...   \n", "371         163  G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...   \n", "372         136  T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...   \n", "373         147  T166C_G167C_T169A_A170C_C171T_C175G_T176G_C178...   \n", "\n", "     Alignment Probability  Alignment Count Row  Column Well  Plate  \\\n", "0                 0.798821            248.0   A       1  A01      1   \n", "1                 0.717116            300.0   A       2  A02      1   \n", "2                 0.572425            410.0   A       3  A03      1   \n", "3                 0.476841            297.0   A       4  A04      1   \n", "4                 0.610185            424.0   A       5  A05      1   \n", "..                     ...              ...  ..     ...  ...    ...   \n", "369               0.235028            156.0   H       8  H08      4   \n", "370               0.363358            455.0   H       9  H09      4   \n", "371               0.545897            270.0   H      10  H10      4   \n", "372               0.783072            208.0   H      11  H11      4   \n", "373               0.450191            136.0   H      12  H12      4   \n", "\n", "          CodonCombo  Combo  \n", "0    ACTAATATGCCTGAG  TNMPE  \n", "1    ACTAATATGCCTGAG  TNMPE  \n", "2    TCTATGAGTCATTGT  SMSHC  \n", "3    GTGTGTAGGGCGTCG  VCRAS  \n", "4    AGTATTGTTAAGACG  SIVKT  \n", "..               ...    ...  \n", "369  ACGTCTGGTATGTGT  TSGMC  \n", "370  ATGTGGCGGACGTTT  MWRTF  \n", "371  TCTATGAGTCATACT  SMSHT  \n", "372  ACTAATATGCCTCAG  TNMPQ  \n", "373  CCGACTGGGGAGTAG  PTGE*  \n", "\n", "[343 rows x 10 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#seq_df = pd.read_csv('merged_seqs_outer.csv')\n", "seq_df = seq_df2\n", "seq_df"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3747626/1265835526.py:9: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df = df.append(fitness_df)\n", "/tmp/ipykernel_3747626/1265835526.py:9: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df = df.append(fitness_df)\n", "/tmp/ipykernel_3747626/1265835526.py:9: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df = df.append(fitness_df)\n", "/tmp/ipykernel_3747626/1265835526.py:9: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df = df.append(fitness_df)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>Variant</th>\n", "      <th>Alignment Probability</th>\n", "      <th>Alignment Count</th>\n", "      <th>Row</th>\n", "      <th>Column</th>\n", "      <th>Well</th>\n", "      <th>Plate</th>\n", "      <th>CodonCombo</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>PlateNormIso2</th>\n", "      <th>StdArea</th>\n", "      <th>Iso1Area</th>\n", "      <th>Iso2Area</th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>PlateNormIso1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>263</td>\n", "      <td>T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...</td>\n", "      <td>0.798821</td>\n", "      <td>248.0</td>\n", "      <td>A</td>\n", "      <td>1</td>\n", "      <td>A01</td>\n", "      <td>1</td>\n", "      <td>ACTAATATGCCTGAG</td>\n", "      <td>TNMPE</td>\n", "      <td>4.949078</td>\n", "      <td>501</td>\n", "      <td>149.0</td>\n", "      <td>3585.0</td>\n", "      <td>0.297405</td>\n", "      <td>7.155689</td>\n", "      <td>0.205694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>262</td>\n", "      <td>T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...</td>\n", "      <td>0.717116</td>\n", "      <td>300.0</td>\n", "      <td>A</td>\n", "      <td>2</td>\n", "      <td>A02</td>\n", "      <td>1</td>\n", "      <td>ACTAATATGCCTGAG</td>\n", "      <td>TNMPE</td>\n", "      <td>4.435962</td>\n", "      <td>493</td>\n", "      <td>224.0</td>\n", "      <td>3162.0</td>\n", "      <td>0.454361</td>\n", "      <td>6.413793</td>\n", "      <td>0.314249</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>186</td>\n", "      <td>G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...</td>\n", "      <td>0.572425</td>\n", "      <td>410.0</td>\n", "      <td>A</td>\n", "      <td>3</td>\n", "      <td>A03</td>\n", "      <td>1</td>\n", "      <td>TCTATGAGTCATTGT</td>\n", "      <td>SMSHC</td>\n", "      <td>3.912760</td>\n", "      <td>499</td>\n", "      <td>588.0</td>\n", "      <td>2823.0</td>\n", "      <td>1.178357</td>\n", "      <td>5.657315</td>\n", "      <td>0.814985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>190</td>\n", "      <td>T166G_G167T_A170G_C171T_C175A_T176G_C178G_A179...</td>\n", "      <td>0.476841</td>\n", "      <td>297.0</td>\n", "      <td>A</td>\n", "      <td>4</td>\n", "      <td>A04</td>\n", "      <td>1</td>\n", "      <td>GTGTGTAGGGCGTCG</td>\n", "      <td>VCRAS</td>\n", "      <td>4.714394</td>\n", "      <td>501</td>\n", "      <td>200.0</td>\n", "      <td>3415.0</td>\n", "      <td>0.399202</td>\n", "      <td>6.816367</td>\n", "      <td>0.276099</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>277</td>\n", "      <td>T166A_G168T_T169A_A170T_C171T_C175G_G177T_C178...</td>\n", "      <td>0.610185</td>\n", "      <td>424.0</td>\n", "      <td>A</td>\n", "      <td>5</td>\n", "      <td>A05</td>\n", "      <td>1</td>\n", "      <td>AGTATTGTTAAGACG</td>\n", "      <td>SIVKT</td>\n", "      <td>4.623207</td>\n", "      <td>504</td>\n", "      <td>356.0</td>\n", "      <td>3369.0</td>\n", "      <td>0.706349</td>\n", "      <td>6.684524</td>\n", "      <td>0.488531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>338</th>\n", "      <td>129</td>\n", "      <td>T166A_G167C_A170C_C171T_C175G_T176G_G177T_C178...</td>\n", "      <td>0.235028</td>\n", "      <td>156.0</td>\n", "      <td>H</td>\n", "      <td>8</td>\n", "      <td>H08</td>\n", "      <td>4</td>\n", "      <td>ACGTCTGGTATGTGT</td>\n", "      <td>TSGMC</td>\n", "      <td>2.943782</td>\n", "      <td>514</td>\n", "      <td>326.0</td>\n", "      <td>2176.0</td>\n", "      <td>0.634241</td>\n", "      <td>4.233463</td>\n", "      <td>0.441026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>339</th>\n", "      <td>120</td>\n", "      <td>T166A_G167T_A170G_C171G_T176G_C178A_A179C_C267...</td>\n", "      <td>0.363358</td>\n", "      <td>455.0</td>\n", "      <td>H</td>\n", "      <td>9</td>\n", "      <td>H09</td>\n", "      <td>4</td>\n", "      <td>ATGTGGCGGACGTTT</td>\n", "      <td>MWRTF</td>\n", "      <td>2.053864</td>\n", "      <td>518</td>\n", "      <td>640.0</td>\n", "      <td>1530.0</td>\n", "      <td>1.235521</td>\n", "      <td>2.953668</td>\n", "      <td>0.859132</td>\n", "    </tr>\n", "    <tr>\n", "      <th>340</th>\n", "      <td>163</td>\n", "      <td>G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...</td>\n", "      <td>0.545897</td>\n", "      <td>270.0</td>\n", "      <td>H</td>\n", "      <td>10</td>\n", "      <td>H10</td>\n", "      <td>4</td>\n", "      <td>TCTATGAGTCATACT</td>\n", "      <td>SMSHT</td>\n", "      <td>3.390388</td>\n", "      <td>515</td>\n", "      <td>439.0</td>\n", "      <td>2511.0</td>\n", "      <td>0.852427</td>\n", "      <td>4.875728</td>\n", "      <td>0.592744</td>\n", "    </tr>\n", "    <tr>\n", "      <th>341</th>\n", "      <td>136</td>\n", "      <td>T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...</td>\n", "      <td>0.783072</td>\n", "      <td>208.0</td>\n", "      <td>H</td>\n", "      <td>11</td>\n", "      <td>H11</td>\n", "      <td>4</td>\n", "      <td>ACTAATATGCCTCAG</td>\n", "      <td>TNMPQ</td>\n", "      <td>3.761516</td>\n", "      <td>508</td>\n", "      <td>217.0</td>\n", "      <td>2748.0</td>\n", "      <td>0.427165</td>\n", "      <td>5.409449</td>\n", "      <td>0.297034</td>\n", "    </tr>\n", "    <tr>\n", "      <th>342</th>\n", "      <td>147</td>\n", "      <td>T166C_G167C_T169A_A170C_C171T_C175G_T176G_C178...</td>\n", "      <td>0.450191</td>\n", "      <td>136.0</td>\n", "      <td>H</td>\n", "      <td>12</td>\n", "      <td>H12</td>\n", "      <td>4</td>\n", "      <td>CCGACTGGGGAGTAG</td>\n", "      <td>PTGE*</td>\n", "      <td>3.757410</td>\n", "      <td>508</td>\n", "      <td>150.0</td>\n", "      <td>2745.0</td>\n", "      <td>0.295276</td>\n", "      <td>5.403543</td>\n", "      <td>0.205323</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>343 rows × 17 columns</p>\n", "</div>"], "text/plain": ["     Unnamed: 0                                            Variant  \\\n", "0           263  T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...   \n", "1           262  T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...   \n", "2           186  G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...   \n", "3           190  T166G_G167T_A170G_C171T_C175A_T176G_C178G_A179...   \n", "4           277  T166A_G168T_T169A_A170T_C171T_C175G_G177T_C178...   \n", "..          ...                                                ...   \n", "338         129  T166A_G167C_A170C_C171T_C175G_T176G_G177T_C178...   \n", "339         120  T166A_G167T_A170G_C171G_T176G_C178A_A179C_C267...   \n", "340         163  G167C_G168T_T169A_A170T_C171G_C175A_T176G_G177...   \n", "341         136  T166A_G167C_G168T_T169A_C171T_C175A_A179C_G180...   \n", "342         147  T166C_G167C_T169A_A170C_C171T_C175G_T176G_C178...   \n", "\n", "     Alignment Probability  Alignment Count Row  Column Well  Plate  \\\n", "0                 0.798821            248.0   A       1  A01      1   \n", "1                 0.717116            300.0   A       2  A02      1   \n", "2                 0.572425            410.0   A       3  A03      1   \n", "3                 0.476841            297.0   A       4  A04      1   \n", "4                 0.610185            424.0   A       5  A05      1   \n", "..                     ...              ...  ..     ...  ...    ...   \n", "338               0.235028            156.0   H       8  H08      4   \n", "339               0.363358            455.0   H       9  H09      4   \n", "340               0.545897            270.0   H      10  H10      4   \n", "341               0.783072            208.0   H      11  H11      4   \n", "342               0.450191            136.0   H      12  H12      4   \n", "\n", "          CodonCombo  Combo  PlateNormIso2  StdArea  Iso1Area  Iso2Area  \\\n", "0    ACTAATATGCCTGAG  TNMPE       4.949078      501     149.0    3585.0   \n", "1    ACTAATATGCCTGAG  TNMPE       4.435962      493     224.0    3162.0   \n", "2    TCTATGAGTCATTGT  SMSHC       3.912760      499     588.0    2823.0   \n", "3    GTGTGTAGGGCGTCG  VCRAS       4.714394      501     200.0    3415.0   \n", "4    AGTATTGTTAAGACG  SIVKT       4.623207      504     356.0    3369.0   \n", "..               ...    ...            ...      ...       ...       ...   \n", "338  ACGTCTGGTATGTGT  TSGMC       2.943782      514     326.0    2176.0   \n", "339  ATGTGGCGGACGTTT  MWRTF       2.053864      518     640.0    1530.0   \n", "340  TCTATGAGTCATACT  SMSHT       3.390388      515     439.0    2511.0   \n", "341  ACTAATATGCCTCAG  TNMPQ       3.761516      508     217.0    2748.0   \n", "342  CCGACTGGGGAGTAG  PTGE*       3.757410      508     150.0    2745.0   \n", "\n", "     NormIso1  NormIso2  PlateNormIso1  \n", "0    0.297405  7.155689       0.205694  \n", "1    0.454361  6.413793       0.314249  \n", "2    1.178357  5.657315       0.814985  \n", "3    0.399202  6.816367       0.276099  \n", "4    0.706349  6.684524       0.488531  \n", "..        ...       ...            ...  \n", "338  0.634241  4.233463       0.441026  \n", "339  1.235521  2.953668       0.859132  \n", "340  0.852427  4.875728       0.592744  \n", "341  0.427165  5.409449       0.297034  \n", "342  0.295276  5.403543       0.205323  \n", "\n", "[343 rows x 17 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame()\n", "for i in range(4):\n", "    fitness_df = pd.read_excel('initial/RL-7-49_Integrations.xlsx', sheet_name=i)\n", "    fitness_df['Plate'] = i + 1\n", "    fitness_df['Well'] = fitness_df['Well'].apply(reformat)\n", "    normalizer = fitness_df[fitness_df['Well'].isin(['B04', 'C05', 'D06', 'E07', 'F08', 'G09'])]['NormIso1'].mean()\n", "    fitness_df['PlateNormIso1'] = fitness_df['NormIso1']/normalizer\n", "    fitness_df['PlateNormIso2'] = fitness_df['NormIso2']/normalizer\n", "    df = df.append(fitness_df)\n", "\n", "#move plate column to the front\n", "cols = df.columns.tolist()\n", "cols = cols[-1:] + cols[:-1]\n", "df = df[cols]\n", "combined = pd.merge(seq_df, df, on=['Plate', 'Well'], how = 'left')\n", "#rename SimpleCombo_evSeq to SimpleCombo\n", "#merged.rename(columns={'SimpleCombo_evSeq': 'SimpleCombo'}, inplace=True)\n", "combined "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["combined.to_csv('inital_well2seq.csv', index=False)"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.29303384586021164 0.1735153518479471\n", "0.8470824949698189\n", "2.7894086501111777 1.7789757084717752\n", "6.390243902439025\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3682401/550777980.py:21: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  indices = cleaned_df[cleaned_df['Combo'] == 'WYLQF'][~cleaned_df['Well'].isin(['B04', 'C05', 'D06', 'E07', 'F08', 'G09'])].index\n", "/tmp/ipykernel_3682401/550777980.py:28: FutureWarning: The default value of numeric_only in DataFrameGroupBy.mean is deprecated. In a future version, numeric_only will default to False. Either specify numeric_only or select only columns which should be valid for the function.\n", "  cleaned_df = cleaned_df.groupby(['Combo']).mean()\n", "/tmp/ipykernel_3682401/550777980.py:21: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  indices = cleaned_df[cleaned_df['Combo'] == 'WYLQF'][~cleaned_df['Well'].isin(['B04', 'C05', 'D06', 'E07', 'F08', 'G09'])].index\n", "/tmp/ipykernel_3682401/550777980.py:28: FutureWarning: The default value of numeric_only in DataFrameGroupBy.mean is deprecated. In a future version, numeric_only will default to False. Either specify numeric_only or select only columns which should be valid for the function.\n", "  cleaned_df = cleaned_df.groupby(['Combo']).mean()\n"]}], "source": ["stop_df1, expanded_df1, merged_cleaned = process(combined, 'NormIso1')\n", "stop_df2, expanded_df2, cleaned_df2 = process(combined, 'NormIso2')"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["merged_cleaned = pd.merge(merged_cleaned.drop('PlateNormIso2', axis=1), cleaned_df2[['Combo', 'PlateNormIso2']])\n", "merged_cleaned['Diff'] = merged_cleaned['PlateNormIso1'] - merged_cleaned['PlateNormIso2']\n", "merged_cleaned = merged_cleaned.sort_values(by=['Diff'], ascending=False)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>PlateNormIso1</th>\n", "      <th>PlateNormIso2</th>\n", "      <th>Diff</th>\n", "      <th>round</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FRMNY</td>\n", "      <td>7.020661</td>\n", "      <td>1.171488</td>\n", "      <td>4.586441</td>\n", "      <td>0.765307</td>\n", "      <td>3.821134</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SAFRY</td>\n", "      <td>7.757700</td>\n", "      <td>1.977413</td>\n", "      <td>5.067932</td>\n", "      <td>1.291800</td>\n", "      <td>3.776133</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GIDLY</td>\n", "      <td>4.960080</td>\n", "      <td>0.854291</td>\n", "      <td>3.449043</td>\n", "      <td>0.594040</td>\n", "      <td>2.855002</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TSGMY</td>\n", "      <td>6.152918</td>\n", "      <td>2.136821</td>\n", "      <td>4.019563</td>\n", "      <td>1.395937</td>\n", "      <td>2.623626</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TNMPY</td>\n", "      <td>3.694215</td>\n", "      <td>0.758264</td>\n", "      <td>3.260248</td>\n", "      <td>0.669190</td>\n", "      <td>2.591058</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>LQSGA</td>\n", "      <td>0.582278</td>\n", "      <td>5.748945</td>\n", "      <td>0.513877</td>\n", "      <td>5.073604</td>\n", "      <td>-4.559727</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>QYKGD</td>\n", "      <td>0.242081</td>\n", "      <td>5.511312</td>\n", "      <td>0.213644</td>\n", "      <td>4.863887</td>\n", "      <td>-4.650243</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>PCLTD</td>\n", "      <td>0.173913</td>\n", "      <td>6.942688</td>\n", "      <td>0.120283</td>\n", "      <td>4.801761</td>\n", "      <td>-4.681477</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>NNVER</td>\n", "      <td>0.208251</td>\n", "      <td>5.593320</td>\n", "      <td>0.183788</td>\n", "      <td>4.936261</td>\n", "      <td>-4.752473</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>TSGRE</td>\n", "      <td>0.218367</td>\n", "      <td>7.134694</td>\n", "      <td>0.151029</td>\n", "      <td>4.934558</td>\n", "      <td>-4.783529</td>\n", "      <td>initial</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>216 rows × 7 columns</p>\n", "</div>"], "text/plain": ["     Combo  NormIso1  NormIso2  PlateNormIso1  PlateNormIso2      Diff  \\\n", "0    FRMNY  7.020661  1.171488       4.586441       0.765307  3.821134   \n", "1    SAFRY  7.757700  1.977413       5.067932       1.291800  3.776133   \n", "2    GIDLY  4.960080  0.854291       3.449043       0.594040  2.855002   \n", "3    TSGMY  6.152918  2.136821       4.019563       1.395937  2.623626   \n", "4    TNMPY  3.694215  0.758264       3.260248       0.669190  2.591058   \n", "..     ...       ...       ...            ...            ...       ...   \n", "211  LQSGA  0.582278  5.748945       0.513877       5.073604 -4.559727   \n", "212  QYKGD  0.242081  5.511312       0.213644       4.863887 -4.650243   \n", "213  PCLTD  0.173913  6.942688       0.120283       4.801761 -4.681477   \n", "214  NNVER  0.208251  5.593320       0.183788       4.936261 -4.752473   \n", "215  TSGRE  0.218367  7.134694       0.151029       4.934558 -4.783529   \n", "\n", "       round  \n", "0    initial  \n", "1    initial  \n", "2    initial  \n", "3    initial  \n", "4    initial  \n", "..       ...  \n", "211  initial  \n", "212  initial  \n", "213  initial  \n", "214  initial  \n", "215  initial  \n", "\n", "[216 rows x 7 columns]"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_cleaned = merged_cleaned[['Combo', 'NormIso1', 'NormIso2', 'PlateNormIso1', 'PlateNormIso2', 'Diff']].reset_index(drop=True)\n", "merged_cleaned['round'] = 'initial'\n", "merged_cleaned"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["merged_cleaned.to_csv('fitness_initial.csv', index=False)"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["inital = merged_cleaned"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Process round 1"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.9502449585000001\n", "1.73073007175\n", "2.0723383265\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3682401/4127792268.py:7: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sub_df['PlateNormIso1'] = sub_df['NormIso1'] / normalizer\n", "/tmp/ipykernel_3682401/4127792268.py:8: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sub_df['PlateNormIso2'] = sub_df['NormIso2'] / normalizer\n", "/tmp/ipykernel_3682401/4127792268.py:7: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sub_df['PlateNormIso1'] = sub_df['NormIso1'] / normalizer\n", "/tmp/ipykernel_3682401/4127792268.py:8: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sub_df['PlateNormIso2'] = sub_df['NormIso2'] / normalizer\n", "/tmp/ipykernel_3682401/4127792268.py:7: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sub_df['PlateNormIso1'] = sub_df['NormIso1'] / normalizer\n", "/tmp/ipykernel_3682401/4127792268.py:8: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sub_df['PlateNormIso2'] = sub_df['NormIso2'] / normalizer\n"]}], "source": ["df = pd.read_csv('round1/RL-7-65_Elegen1_Integrations.csv')\n", "dfs = []\n", "for plate in df['Replicate'].unique():\n", "    sub_df = df[df['Replicate'] == plate]\n", "    normalizer = sub_df[sub_df['Type'] == 'Parent']['NormIso1'].mean()\n", "    print(normalizer)\n", "    sub_df['PlateNormIso1'] = sub_df['NormIso1'] / normalizer\n", "    sub_df['PlateNormIso2'] = sub_df['NormIso2'] / normalizer\n", "    dfs.append(sub_df)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3682401/1232806211.py:1: FutureWarning: The default value of numeric_only in DataFrameGroupBy.mean is deprecated. In a future version, numeric_only will default to False. Either specify numeric_only or select only columns which should be valid for the function.\n", "  round2  = pd.concat(dfs).groupby('Variant', as_index=False).mean()\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>PlateNormIso1</th>\n", "      <th>PlateNormIso2</th>\n", "      <th>Diff</th>\n", "      <th>round</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>MKFNY</td>\n", "      <td>9.432613</td>\n", "      <td>0.856375</td>\n", "      <td>4.931448</td>\n", "      <td>0.446740</td>\n", "      <td>4.484708</td>\n", "      <td>round1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>FYFNY</td>\n", "      <td>9.008440</td>\n", "      <td>1.135131</td>\n", "      <td>4.721267</td>\n", "      <td>0.595250</td>\n", "      <td>4.126016</td>\n", "      <td>round1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>FKFNY</td>\n", "      <td>8.780988</td>\n", "      <td>1.111163</td>\n", "      <td>4.580466</td>\n", "      <td>0.578125</td>\n", "      <td>4.002340</td>\n", "      <td>round1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>FKTNY</td>\n", "      <td>8.959874</td>\n", "      <td>1.541843</td>\n", "      <td>4.697543</td>\n", "      <td>0.808106</td>\n", "      <td>3.889436</td>\n", "      <td>round1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>GKFNY</td>\n", "      <td>8.463420</td>\n", "      <td>1.033273</td>\n", "      <td>4.415658</td>\n", "      <td>0.538264</td>\n", "      <td>3.877394</td>\n", "      <td>round1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>FTFLY</td>\n", "      <td>3.053174</td>\n", "      <td>1.948142</td>\n", "      <td>1.597961</td>\n", "      <td>1.021748</td>\n", "      <td>0.576213</td>\n", "      <td>round1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>FIFLY</td>\n", "      <td>2.244332</td>\n", "      <td>1.722342</td>\n", "      <td>1.175614</td>\n", "      <td>0.904734</td>\n", "      <td>0.270880</td>\n", "      <td>round1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>FFFLY</td>\n", "      <td>1.711009</td>\n", "      <td>1.487548</td>\n", "      <td>0.901995</td>\n", "      <td>0.784286</td>\n", "      <td>0.117709</td>\n", "      <td>round1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>FCFLY</td>\n", "      <td>2.087210</td>\n", "      <td>2.290904</td>\n", "      <td>1.093142</td>\n", "      <td>1.201747</td>\n", "      <td>-0.108605</td>\n", "      <td>round1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>QIGNY</td>\n", "      <td>3.732436</td>\n", "      <td>4.105076</td>\n", "      <td>1.961609</td>\n", "      <td>2.150922</td>\n", "      <td>-0.189314</td>\n", "      <td>round1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>90 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    Combo  NormIso1  NormIso2  PlateNormIso1  PlateNormIso2      Diff   round\n", "74  MKFNY  9.432613  0.856375       4.931448       0.446740  4.484708  round1\n", "50  FYFNY  9.008440  1.135131       4.721267       0.595250  4.126016  round1\n", "31  FKFNY  8.780988  1.111163       4.580466       0.578125  4.002340  round1\n", "33  FKTNY  8.959874  1.541843       4.697543       0.808106  3.889436  round1\n", "59  GKFNY  8.463420  1.033273       4.415658       0.538264  3.877394  round1\n", "..    ...       ...       ...            ...            ...       ...     ...\n", "47  FTFLY  3.053174  1.948142       1.597961       1.021748  0.576213  round1\n", "25  FIFLY  2.244332  1.722342       1.175614       0.904734  0.270880  round1\n", "20  FFFLY  1.711009  1.487548       0.901995       0.784286  0.117709  round1\n", "17  FCFLY  2.087210  2.290904       1.093142       1.201747 -0.108605  round1\n", "81  QIGNY  3.732436  4.105076       1.961609       2.150922 -0.189314  round1\n", "\n", "[90 rows x 7 columns]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["round2  = pd.concat(dfs).groupby('Variant', as_index=False).mean()\n", "round2['Diff'] = round2['PlateNormIso1'] - round2['PlateNormIso2']\n", "round2 = round2.sort_values('Diff', ascending=False)\n", "round2 = round2[round2['Variant'] != 'Sterile']\n", "round2 = round2[round2['Variant'] != 'WYLQF'] #drop parent from this round (just use old)\n", "round2\n", "round2.rename(columns={'Variant': 'Combo'}, inplace=True)\n", "round2['round'] = 'round1'\n", "\n", "round2 = round2[['Combo', 'NormIso1', 'NormIso2', 'PlateNormIso1', 'PlateNormIso2', 'Diff', 'round']]\n", "round1 = round2\n", "round2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now process the second round results"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.7348956060437493\n", "0.9837005214918702\n", "1.6163460949461421\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3682401/166235996.py:20: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df[feature] = pd.concat([df1[feature], df2[feature], df3[feature]], axis=1).mean(axis=1)\n", "/tmp/ipykernel_3682401/166235996.py:20: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df[feature] = pd.concat([df1[feature], df2[feature], df3[feature]], axis=1).mean(axis=1)\n", "/tmp/ipykernel_3682401/166235996.py:20: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df[feature] = pd.concat([df1[feature], df2[feature], df3[feature]], axis=1).mean(axis=1)\n", "/tmp/ipykernel_3682401/166235996.py:20: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df[feature] = pd.concat([df1[feature], df2[feature], df3[feature]], axis=1).mean(axis=1)\n", "/tmp/ipykernel_3682401/166235996.py:23: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  round2['Diff'] = round2['PlateNormIso1'] - round2['PlateNormIso2']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>PlateNormIso1</th>\n", "      <th>PlateNormIso2</th>\n", "      <th>Diff</th>\n", "      <th>round</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>MPFDY</td>\n", "      <td>10.180141</td>\n", "      <td>0.805376</td>\n", "      <td>7.494145</td>\n", "      <td>0.589870</td>\n", "      <td>6.904275</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>MGFDY</td>\n", "      <td>9.195301</td>\n", "      <td>0.542126</td>\n", "      <td>6.632599</td>\n", "      <td>0.394564</td>\n", "      <td>6.238035</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>FKMDY</td>\n", "      <td>9.531129</td>\n", "      <td>1.417157</td>\n", "      <td>7.092939</td>\n", "      <td>1.043002</td>\n", "      <td>6.049937</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>HKFNY</td>\n", "      <td>8.940092</td>\n", "      <td>0.819603</td>\n", "      <td>6.571582</td>\n", "      <td>0.602854</td>\n", "      <td>5.968727</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>FKMAY</td>\n", "      <td>8.958074</td>\n", "      <td>0.899219</td>\n", "      <td>6.432142</td>\n", "      <td>0.646406</td>\n", "      <td>5.785736</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>MYFAW</td>\n", "      <td>2.728117</td>\n", "      <td>1.853367</td>\n", "      <td>1.975724</td>\n", "      <td>1.355883</td>\n", "      <td>0.619842</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>MEMDW</td>\n", "      <td>3.325736</td>\n", "      <td>2.541647</td>\n", "      <td>2.408365</td>\n", "      <td>1.840167</td>\n", "      <td>0.568198</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>MYMDW</td>\n", "      <td>2.098371</td>\n", "      <td>2.363978</td>\n", "      <td>1.466306</td>\n", "      <td>1.693301</td>\n", "      <td>-0.226994</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>GPFAW</td>\n", "      <td>0.218307</td>\n", "      <td>1.211705</td>\n", "      <td>0.154663</td>\n", "      <td>0.866176</td>\n", "      <td>-0.711513</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>MGFVW</td>\n", "      <td>2.309565</td>\n", "      <td>3.490712</td>\n", "      <td>1.623300</td>\n", "      <td>2.554560</td>\n", "      <td>-0.931260</td>\n", "      <td>round2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>90 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    Combo   NormIso1  NormIso2  PlateNormIso1  PlateNormIso2      Diff   round\n", "57  MPFDY  10.180141  0.805376       7.494145       0.589870  6.904275  round2\n", "74  MGFDY   9.195301  0.542126       6.632599       0.394564  6.238035  round2\n", "11  FKMDY   9.531129  1.417157       7.092939       1.043002  6.049937  round2\n", "25  HKFNY   8.940092  0.819603       6.571582       0.602854  5.968727  round2\n", "44  FKMAY   8.958074  0.899219       6.432142       0.646406  5.785736  round2\n", "..    ...        ...       ...            ...            ...       ...     ...\n", "64  MYFAW   2.728117  1.853367       1.975724       1.355883  0.619842  round2\n", "90  MEMDW   3.325736  2.541647       2.408365       1.840167  0.568198  round2\n", "65  MYMDW   2.098371  2.363978       1.466306       1.693301 -0.226994  round2\n", "66  GPFAW   0.218307  1.211705       0.154663       0.866176 -0.711513  round2\n", "77  MGFVW   2.309565  3.490712       1.623300       2.554560 -0.931260  round2\n", "\n", "[90 rows x 7 columns]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["def normalize_plate(sub_df):\n", "    normalizer = sub_df[sub_df['Type'] == 'Parent']['NormIso1'].mean()\n", "    print(normalizer)\n", "    sub_df['PlateNormIso1'] = sub_df['NormIso1'] / normalizer\n", "    sub_df['PlateNormIso2'] = sub_df['NormIso2'] / normalizer\n", "    return sub_df\n", "\n", "df1 = pd.read_excel('round2/RL-7-77_Elegen2_Trip1_Data.xls')\n", "df2 = pd.read_excel('round2/RL-7-77_Elegen2_Trip2_Data.xls')\n", "df3 = pd.read_excel('round2/RL-7-77_Elegen2_Trip3_Data.xls')\n", "\n", "df1 = normalize_plate(df1)\n", "df2 = normalize_plate(df2)\n", "df3 = normalize_plate(df3)\n", "\n", "df = df1[['Well', 'Type', 'Variant']]\n", "\n", "#concatenate the normiso1 columns from the three dataframes\n", "for feature in ['NormIso1', 'NormIso2', 'PlateNormIso1', 'PlateNormIso2']:\n", "    df[feature] = pd.concat([df1[feature], df2[feature], df3[feature]], axis=1).mean(axis=1)\n", "\n", "round2 = df\n", "round2['Diff'] = round2['PlateNormIso1'] - round2['PlateNormIso2']\n", "round2 = round2.sort_values('Diff', ascending=False)\n", "round2 = round2[round2['Type'] != 'Sterile']\n", "round2 = round2[round2['Type'] != 'Parent']\n", "round2\n", "round2.rename(columns={'Variant': 'Combo'}, inplace=True)\n", "round2['round'] = 'round2'\n", "\n", "round2 = round2[['Combo', 'NormIso1', 'NormIso2', 'PlateNormIso1', 'PlateNormIso2', 'Diff', 'round']]\n", "round2\n", "\n", "round2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now put it all together"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>PlateNormIso1</th>\n", "      <th>PlateNormIso2</th>\n", "      <th>Diff</th>\n", "      <th>round</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FRMNY</td>\n", "      <td>7.020661</td>\n", "      <td>1.171488</td>\n", "      <td>4.586441</td>\n", "      <td>0.765307</td>\n", "      <td>3.821134</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SAFRY</td>\n", "      <td>7.757700</td>\n", "      <td>1.977413</td>\n", "      <td>5.067932</td>\n", "      <td>1.291800</td>\n", "      <td>3.776133</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GIDLY</td>\n", "      <td>4.960080</td>\n", "      <td>0.854291</td>\n", "      <td>3.449043</td>\n", "      <td>0.594040</td>\n", "      <td>2.855002</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TSGMY</td>\n", "      <td>6.152918</td>\n", "      <td>2.136821</td>\n", "      <td>4.019563</td>\n", "      <td>1.395937</td>\n", "      <td>2.623626</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TNMPY</td>\n", "      <td>3.694215</td>\n", "      <td>0.758264</td>\n", "      <td>3.260248</td>\n", "      <td>0.669190</td>\n", "      <td>2.591058</td>\n", "      <td>initial</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>MYFAW</td>\n", "      <td>2.728117</td>\n", "      <td>1.853367</td>\n", "      <td>1.975724</td>\n", "      <td>1.355883</td>\n", "      <td>0.619842</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>MEMDW</td>\n", "      <td>3.325736</td>\n", "      <td>2.541647</td>\n", "      <td>2.408365</td>\n", "      <td>1.840167</td>\n", "      <td>0.568198</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>MYMDW</td>\n", "      <td>2.098371</td>\n", "      <td>2.363978</td>\n", "      <td>1.466306</td>\n", "      <td>1.693301</td>\n", "      <td>-0.226994</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>GPFAW</td>\n", "      <td>0.218307</td>\n", "      <td>1.211705</td>\n", "      <td>0.154663</td>\n", "      <td>0.866176</td>\n", "      <td>-0.711513</td>\n", "      <td>round2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>MGFVW</td>\n", "      <td>2.309565</td>\n", "      <td>3.490712</td>\n", "      <td>1.623300</td>\n", "      <td>2.554560</td>\n", "      <td>-0.931260</td>\n", "      <td>round2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>396 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    Combo  NormIso1  NormIso2  PlateNormIso1  PlateNormIso2      Diff    round\n", "0   FRMNY  7.020661  1.171488       4.586441       0.765307  3.821134  initial\n", "1   SAFRY  7.757700  1.977413       5.067932       1.291800  3.776133  initial\n", "2   GIDLY  4.960080  0.854291       3.449043       0.594040  2.855002  initial\n", "3   TSGMY  6.152918  2.136821       4.019563       1.395937  2.623626  initial\n", "4   TNMPY  3.694215  0.758264       3.260248       0.669190  2.591058  initial\n", "..    ...       ...       ...            ...            ...       ...      ...\n", "64  MYFAW  2.728117  1.853367       1.975724       1.355883  0.619842   round2\n", "90  MEMDW  3.325736  2.541647       2.408365       1.840167  0.568198   round2\n", "65  MYMDW  2.098371  2.363978       1.466306       1.693301 -0.226994   round2\n", "66  GPFAW  0.218307  1.211705       0.154663       0.866176 -0.711513   round2\n", "77  MGFVW  2.309565  3.490712       1.623300       2.554560 -0.931260   round2\n", "\n", "[396 rows x 7 columns]"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["total = pd.concat([inital, round1, round2])\n", "total"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [], "source": ["#from ravi's benchling calibration curve\n", "def normiso1toyield(x):\n", "    return (x - 0.0738)/1.6361 * 10 * 1.5\n", "\n", "def normiso2toyield(x):\n", "    return (x - 0.0978)/1.6067 * 10 * 1.5"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>PlateNormIso1</th>\n", "      <th>PlateNormIso2</th>\n", "      <th>Diff</th>\n", "      <th>round</th>\n", "      <th>yield1</th>\n", "      <th>yield2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FRMNY</td>\n", "      <td>7.020661</td>\n", "      <td>1.171488</td>\n", "      <td>4.586441</td>\n", "      <td>0.765307</td>\n", "      <td>3.821134</td>\n", "      <td>initial</td>\n", "      <td>63.689822</td>\n", "      <td>10.023846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SAFRY</td>\n", "      <td>7.757700</td>\n", "      <td>1.977413</td>\n", "      <td>5.067932</td>\n", "      <td>1.291800</td>\n", "      <td>3.776133</td>\n", "      <td>initial</td>\n", "      <td>70.447102</td>\n", "      <td>17.547888</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GIDLY</td>\n", "      <td>4.960080</td>\n", "      <td>0.854291</td>\n", "      <td>3.449043</td>\n", "      <td>0.594040</td>\n", "      <td>2.855002</td>\n", "      <td>initial</td>\n", "      <td>44.798116</td>\n", "      <td>7.062533</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TSGMY</td>\n", "      <td>6.152918</td>\n", "      <td>2.136821</td>\n", "      <td>4.019563</td>\n", "      <td>1.395937</td>\n", "      <td>2.623626</td>\n", "      <td>initial</td>\n", "      <td>55.734223</td>\n", "      <td>19.036107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TNMPY</td>\n", "      <td>3.694215</td>\n", "      <td>0.758264</td>\n", "      <td>3.260248</td>\n", "      <td>0.669190</td>\n", "      <td>2.591058</td>\n", "      <td>initial</td>\n", "      <td>33.192484</td>\n", "      <td>6.166034</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>MYFAW</td>\n", "      <td>2.728117</td>\n", "      <td>1.853367</td>\n", "      <td>1.975724</td>\n", "      <td>1.355883</td>\n", "      <td>0.619842</td>\n", "      <td>round2</td>\n", "      <td>24.335155</td>\n", "      <td>16.389812</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>MEMDW</td>\n", "      <td>3.325736</td>\n", "      <td>2.541647</td>\n", "      <td>2.408365</td>\n", "      <td>1.840167</td>\n", "      <td>0.568198</td>\n", "      <td>round2</td>\n", "      <td>29.814219</td>\n", "      <td>22.815524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>MYMDW</td>\n", "      <td>2.098371</td>\n", "      <td>2.363978</td>\n", "      <td>1.466306</td>\n", "      <td>1.693301</td>\n", "      <td>-0.226994</td>\n", "      <td>round2</td>\n", "      <td>18.561559</td>\n", "      <td>21.156829</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>GPFAW</td>\n", "      <td>0.218307</td>\n", "      <td>1.211705</td>\n", "      <td>0.154663</td>\n", "      <td>0.866176</td>\n", "      <td>-0.711513</td>\n", "      <td>round2</td>\n", "      <td>1.324859</td>\n", "      <td>10.399309</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>MGFVW</td>\n", "      <td>2.309565</td>\n", "      <td>3.490712</td>\n", "      <td>1.623300</td>\n", "      <td>2.554560</td>\n", "      <td>-0.931260</td>\n", "      <td>round2</td>\n", "      <td>20.497819</td>\n", "      <td>31.675903</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>396 rows × 9 columns</p>\n", "</div>"], "text/plain": ["    Combo  NormIso1  NormIso2  PlateNormIso1  PlateNormIso2      Diff  \\\n", "0   FRMNY  7.020661  1.171488       4.586441       0.765307  3.821134   \n", "1   SAFRY  7.757700  1.977413       5.067932       1.291800  3.776133   \n", "2   GIDLY  4.960080  0.854291       3.449043       0.594040  2.855002   \n", "3   TSGMY  6.152918  2.136821       4.019563       1.395937  2.623626   \n", "4   TNMPY  3.694215  0.758264       3.260248       0.669190  2.591058   \n", "..    ...       ...       ...            ...            ...       ...   \n", "64  MYFAW  2.728117  1.853367       1.975724       1.355883  0.619842   \n", "90  MEMDW  3.325736  2.541647       2.408365       1.840167  0.568198   \n", "65  MYMDW  2.098371  2.363978       1.466306       1.693301 -0.226994   \n", "66  GPFAW  0.218307  1.211705       0.154663       0.866176 -0.711513   \n", "77  MGFVW  2.309565  3.490712       1.623300       2.554560 -0.931260   \n", "\n", "      round     yield1     yield2  \n", "0   initial  63.689822  10.023846  \n", "1   initial  70.447102  17.547888  \n", "2   initial  44.798116   7.062533  \n", "3   initial  55.734223  19.036107  \n", "4   initial  33.192484   6.166034  \n", "..      ...        ...        ...  \n", "64   round2  24.335155  16.389812  \n", "90   round2  29.814219  22.815524  \n", "65   round2  18.561559  21.156829  \n", "66   round2   1.324859  10.399309  \n", "77   round2  20.497819  31.675903  \n", "\n", "[396 rows x 9 columns]"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["total['yield1'] = total['NormIso1'].apply(normiso1toyield)\n", "total['yield2'] = total['NormIso2'].apply(normiso2toyield)\n", "total"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [], "source": ["total.loc[total['Combo'] == 'WYLQF', 'round'] = 'parent'\n", "#move row to top\n", "#total = pd.concat([total[total['round'] == 'parent'], total[total['round'] != 'parent']])"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [], "source": ["total.to_csv('fitness_all.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### some old stuff to consider"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>Alignment Probability</th>\n", "      <th>Alignment Count</th>\n", "      <th>Column</th>\n", "      <th>StdArea</th>\n", "      <th>Iso1Area</th>\n", "      <th>Iso2Area</th>\n", "      <th>num_wells</th>\n", "      <th>EVMutation</th>\n", "      <th>EVMutation Rank</th>\n", "      <th>Triad Score</th>\n", "      <th>Triad Rank</th>\n", "      <th>NormIso1_recomb</th>\n", "      <th>NormIso2_recomb</th>\n", "      <th>NormIso1_recomb_rank</th>\n", "      <th>NormIso2_recomb_rank</th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>Diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FRMNY</td>\n", "      <td>7.000000</td>\n", "      <td>0.624151</td>\n", "      <td>339.00</td>\n", "      <td>2.0</td>\n", "      <td>484.000000</td>\n", "      <td>3398.000000</td>\n", "      <td>567.000000</td>\n", "      <td>1</td>\n", "      <td>-28.982647</td>\n", "      <td>69.0</td>\n", "      <td>-648.97722</td>\n", "      <td>33</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.586441</td>\n", "      <td>0.765307</td>\n", "      <td>3.821134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SAFRY</td>\n", "      <td>2.000000</td>\n", "      <td>0.755508</td>\n", "      <td>128.00</td>\n", "      <td>8.0</td>\n", "      <td>487.000000</td>\n", "      <td>3778.000000</td>\n", "      <td>963.000000</td>\n", "      <td>1</td>\n", "      <td>-31.842763</td>\n", "      <td>118.0</td>\n", "      <td>-646.67160</td>\n", "      <td>48</td>\n", "      <td>0.835324</td>\n", "      <td>1.329356</td>\n", "      <td>20.0</td>\n", "      <td>70.0</td>\n", "      <td>5.067932</td>\n", "      <td>1.291800</td>\n", "      <td>3.776133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GIDLY</td>\n", "      <td>165.000000</td>\n", "      <td>0.086459</td>\n", "      <td>197.00</td>\n", "      <td>5.0</td>\n", "      <td>501.000000</td>\n", "      <td>2485.000000</td>\n", "      <td>428.000000</td>\n", "      <td>1</td>\n", "      <td>-31.299863</td>\n", "      <td>112.0</td>\n", "      <td>-642.32678</td>\n", "      <td>97</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>155.5</td>\n", "      <td>155.5</td>\n", "      <td>3.449043</td>\n", "      <td>0.594040</td>\n", "      <td>2.855002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TSGMY</td>\n", "      <td>28.000000</td>\n", "      <td>0.244922</td>\n", "      <td>154.00</td>\n", "      <td>3.0</td>\n", "      <td>497.000000</td>\n", "      <td>3058.000000</td>\n", "      <td>1062.000000</td>\n", "      <td>1</td>\n", "      <td>-29.967584</td>\n", "      <td>90.0</td>\n", "      <td>-645.07324</td>\n", "      <td>60</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.019563</td>\n", "      <td>1.395937</td>\n", "      <td>2.623626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>TNMPY</td>\n", "      <td>331.000000</td>\n", "      <td>0.867393</td>\n", "      <td>241.00</td>\n", "      <td>11.0</td>\n", "      <td>484.000000</td>\n", "      <td>1788.000000</td>\n", "      <td>367.000000</td>\n", "      <td>1</td>\n", "      <td>-33.636411</td>\n", "      <td>146.0</td>\n", "      <td>-633.74115</td>\n", "      <td>162</td>\n", "      <td>0.262932</td>\n", "      <td>0.495441</td>\n", "      <td>52.0</td>\n", "      <td>132.0</td>\n", "      <td>3.260248</td>\n", "      <td>0.669190</td>\n", "      <td>2.591058</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>QYKGD</td>\n", "      <td>286.000000</td>\n", "      <td>0.001800</td>\n", "      <td>177.00</td>\n", "      <td>4.0</td>\n", "      <td>442.000000</td>\n", "      <td>107.000000</td>\n", "      <td>2436.000000</td>\n", "      <td>1</td>\n", "      <td>-29.577335</td>\n", "      <td>80.0</td>\n", "      <td>-641.25164</td>\n", "      <td>113</td>\n", "      <td>0.390436</td>\n", "      <td>4.028886</td>\n", "      <td>38.0</td>\n", "      <td>2.0</td>\n", "      <td>0.213644</td>\n", "      <td>4.863887</td>\n", "      <td>-4.650243</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>PCLTD</td>\n", "      <td>251.000000</td>\n", "      <td>0.717479</td>\n", "      <td>513.00</td>\n", "      <td>1.0</td>\n", "      <td>506.000000</td>\n", "      <td>88.000000</td>\n", "      <td>3513.000000</td>\n", "      <td>1</td>\n", "      <td>-38.626022</td>\n", "      <td>216.0</td>\n", "      <td>-629.35627</td>\n", "      <td>187</td>\n", "      <td>0.075245</td>\n", "      <td>2.350554</td>\n", "      <td>87.0</td>\n", "      <td>19.0</td>\n", "      <td>0.120283</td>\n", "      <td>4.801761</td>\n", "      <td>-4.681477</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>NNVER</td>\n", "      <td>306.000000</td>\n", "      <td>0.687294</td>\n", "      <td>230.00</td>\n", "      <td>7.0</td>\n", "      <td>509.000000</td>\n", "      <td>106.000000</td>\n", "      <td>2847.000000</td>\n", "      <td>1</td>\n", "      <td>-34.952975</td>\n", "      <td>176.0</td>\n", "      <td>-640.64290</td>\n", "      <td>120</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.183788</td>\n", "      <td>4.936261</td>\n", "      <td>-4.752473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>TSGRE</td>\n", "      <td>193.000000</td>\n", "      <td>0.001915</td>\n", "      <td>556.00</td>\n", "      <td>2.0</td>\n", "      <td>490.000000</td>\n", "      <td>107.000000</td>\n", "      <td>3496.000000</td>\n", "      <td>1</td>\n", "      <td>-35.417786</td>\n", "      <td>185.0</td>\n", "      <td>-639.24294</td>\n", "      <td>135</td>\n", "      <td>0.093254</td>\n", "      <td>1.108917</td>\n", "      <td>79.0</td>\n", "      <td>83.0</td>\n", "      <td>0.151029</td>\n", "      <td>4.934558</td>\n", "      <td>-4.783529</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>WYLQF</td>\n", "      <td>187.416667</td>\n", "      <td>0.960474</td>\n", "      <td>227.25</td>\n", "      <td>6.5</td>\n", "      <td>491.583333</td>\n", "      <td>682.208333</td>\n", "      <td>2156.083333</td>\n", "      <td>24</td>\n", "      <td>-10.557092</td>\n", "      <td>1.0</td>\n", "      <td>-663.60981</td>\n", "      <td>2</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>17.0</td>\n", "      <td>88.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>216 rows × 20 columns</p>\n", "</div>"], "text/plain": ["     Combo  Unnamed: 0  Alignment Probability  Alignment Count  Column  \\\n", "0    FRMNY    7.000000               0.624151           339.00     2.0   \n", "1    SAFRY    2.000000               0.755508           128.00     8.0   \n", "2    GIDLY  165.000000               0.086459           197.00     5.0   \n", "3    TSGMY   28.000000               0.244922           154.00     3.0   \n", "5    TNMPY  331.000000               0.867393           241.00    11.0   \n", "..     ...         ...                    ...              ...     ...   \n", "186  QYKGD  286.000000               0.001800           177.00     4.0   \n", "213  PCLTD  251.000000               0.717479           513.00     1.0   \n", "190  NNVER  306.000000               0.687294           230.00     7.0   \n", "215  TSGRE  193.000000               0.001915           556.00     2.0   \n", "82   WYLQF  187.416667               0.960474           227.25     6.5   \n", "\n", "        StdArea     Iso1Area     Iso2Area  num_wells  EVMutation  \\\n", "0    484.000000  3398.000000   567.000000          1  -28.982647   \n", "1    487.000000  3778.000000   963.000000          1  -31.842763   \n", "2    501.000000  2485.000000   428.000000          1  -31.299863   \n", "3    497.000000  3058.000000  1062.000000          1  -29.967584   \n", "5    484.000000  1788.000000   367.000000          1  -33.636411   \n", "..          ...          ...          ...        ...         ...   \n", "186  442.000000   107.000000  2436.000000          1  -29.577335   \n", "213  506.000000    88.000000  3513.000000          1  -38.626022   \n", "190  509.000000   106.000000  2847.000000          1  -34.952975   \n", "215  490.000000   107.000000  3496.000000          1  -35.417786   \n", "82   491.583333   682.208333  2156.083333         24  -10.557092   \n", "\n", "     EVMutation Rank  Triad Score  Triad Rank  NormIso1_recomb  \\\n", "0               69.0   -648.97722          33              NaN   \n", "1              118.0   -646.67160          48         0.835324   \n", "2              112.0   -642.32678          97         0.000000   \n", "3               90.0   -645.07324          60              NaN   \n", "5              146.0   -633.74115         162         0.262932   \n", "..               ...          ...         ...              ...   \n", "186             80.0   -641.25164         113         0.390436   \n", "213            216.0   -629.35627         187         0.075245   \n", "190            176.0   -640.64290         120              NaN   \n", "215            185.0   -639.24294         135         0.093254   \n", "82               1.0   -663.60981           2         1.000000   \n", "\n", "     NormIso2_recomb  NormIso1_recomb_rank  NormIso2_recomb_rank  NormIso1  \\\n", "0                NaN                   NaN                   NaN  4.586441   \n", "1           1.329356                  20.0                  70.0  5.067932   \n", "2           0.000000                 155.5                 155.5  3.449043   \n", "3                NaN                   NaN                   NaN  4.019563   \n", "5           0.495441                  52.0                 132.0  3.260248   \n", "..               ...                   ...                   ...       ...   \n", "186         4.028886                  38.0                   2.0  0.213644   \n", "213         2.350554                  87.0                  19.0  0.120283   \n", "190              NaN                   NaN                   NaN  0.183788   \n", "215         1.108917                  79.0                  83.0  0.151029   \n", "82          1.000000                  17.0                  88.0       NaN   \n", "\n", "     NormIso2      Diff  \n", "0    0.765307  3.821134  \n", "1    1.291800  3.776133  \n", "2    0.594040  2.855002  \n", "3    1.395937  2.623626  \n", "5    0.669190  2.591058  \n", "..        ...       ...  \n", "186  4.863887 -4.650243  \n", "213  4.801761 -4.681477  \n", "190  4.936261 -4.752473  \n", "215  4.934558 -4.783529  \n", "82        NaN       NaN  \n", "\n", "[216 rows x 20 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["old_df = pd.read_csv('/disk1/jyang4/repos/data/Pgb_fitness_unnormalized.csv')\n", "new = pd.merge(old_df.drop(['NormIso1', 'NormIso2', 'Diff'], axis = 1), merged_cleaned[['Combo', 'NormIso1', 'NormIso2', 'Diff']], on=['Combo'], how='left')\n", "new = new.sort_values(by=['Diff'], ascending=False)\n", "new"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add on the triad scores"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def load_input(triad_output_file, WT_combo, num_seqs):\n", "    \n", "    # Load the output file\n", "    with open(triad_output_file) as f:\n", "\n", "        # Set some flags for starting analysis\n", "        solutions_started = False\n", "        record_start = False\n", "\n", "        # Begin looping over the file\n", "        summary_lines = []\n", "        for line in f:\n", "\n", "            # Start looking at data once we hit \"solution\"\n", "            #if \"Solution\" in line:\n", "            if \"All sequences:\" in line:\n", "                solutions_started = True\n", "\n", "            # Once we have \"Index\" we can start recording the rest\n", "            if solutions_started and \"Index\" in line:\n", "                record_start = True\n", "\n", "            # Record appropriate data\n", "            if record_start:\n", "\n", "                # Strip the newline and split on whitespace\n", "                summary_line = line.strip().split()\n", "\n", "                # If we have hit \"Average\" stop the whole look\n", "                #if summary_line[0] == \"Average\":\n", "                #    break\n", "\n", "                # Otherwise, append the line\n", "                summary_lines.append(summary_line)\n", "\n", "                #if summary_line[0] == '7999':\n", "                if summary_line[0] == str(num_seqs):\n", "                    break\n", "\n", "    # Build the dataframe\n", "    all_results = pd.DataFrame(summary_lines[1:], columns = summary_lines[0])\n", "    all_results[\"Triad Score\"] = all_results.Score.astype(float)\n", "    \n", "    wt_chars = WT_combo\n", "    reconstructed_combos = [\"\".join([char if char != \"-\" else wt_chars[i] for i, char in enumerate(seq)])\n", "                            for seq in all_results.Seq.values]\n", "    all_results[\"Combo\"] = reconstructed_combos\n", "\n", "    # Attach fitness\n", "    #all_results[\"Fitness\"] = all_results.Fitness.values / all_results.Fitness.values.max()\n", "    \n", "    #all_results.loc[all_results['Muts'] == 'WT', 'Fitness'] = 1\n", "    #all_results = all_results[all_results['Fitness'].notna()]\n", "    # Get the order\n", "    all_results[\"Triad Rank\"] = np.arange(1, len(all_results) + 1)\n", "    \n", "    # Split aas into different columns\n", "    #all_results[[\"AA1\", \"AA2\", \"AA3\", \"AA4\"]] = all_results.Combo.apply(lambda x: pd.Series(list(x)))\n", "    \n", "    return all_results\n", "\n", "all_df = load_input('ParPgb_5site_triad_fixed.txt', 'WYWVF', 216)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Index</th>\n", "      <th>Tags</th>\n", "      <th>Score</th>\n", "      <th>Seq</th>\n", "      <th>Muts</th>\n", "      <th>Triad Score</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Triad Rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>WT</td>\n", "      <td>-666.30091</td>\n", "      <td>-----</td>\n", "      <td>WT</td>\n", "      <td>-666.30091</td>\n", "      <td>WYWVF</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>A_59L+A_60Q,59</td>\n", "      <td>-663.60981</td>\n", "      <td>--LQ-</td>\n", "      <td>A_59L+A_60Q</td>\n", "      <td>-663.60981</td>\n", "      <td>WYLQF</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>A_59Q+A_60Q,181</td>\n", "      <td>-661.23234</td>\n", "      <td>--QQ-</td>\n", "      <td>A_59Q+A_60Q</td>\n", "      <td>-661.23234</td>\n", "      <td>WYQQF</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>A_56H+A_57L+A_59V+A_60L+A_89W,121</td>\n", "      <td>-656.70887</td>\n", "      <td>HLVLW</td>\n", "      <td>A_56H+A_57L+A_59V+A_60L+A_89W</td>\n", "      <td>-656.70887</td>\n", "      <td>HLVLW</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>A_56S+A_57W+A_60S+A_89W,138</td>\n", "      <td>-656.62676</td>\n", "      <td>SW-SW</td>\n", "      <td>A_56S+A_57W+A_60S+A_89W</td>\n", "      <td>-656.62676</td>\n", "      <td>SWWSW</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>212</td>\n", "      <td>A_56P+A_57P+A_59H+A_60N+A_89G,63</td>\n", "      <td>-615.69023</td>\n", "      <td>PPHNG</td>\n", "      <td>A_56P+A_57P+A_59H+A_60N+A_89G</td>\n", "      <td>-615.69023</td>\n", "      <td>PPHNG</td>\n", "      <td>213</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>213</td>\n", "      <td>A_56P+A_57A+A_59S+A_60L+A_89P,130</td>\n", "      <td>-615.60940</td>\n", "      <td>PASLP</td>\n", "      <td>A_56P+A_57A+A_59S+A_60L+A_89P</td>\n", "      <td>-615.60940</td>\n", "      <td>PASLP</td>\n", "      <td>214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>214</td>\n", "      <td>A_56T+A_57N+A_59M+A_60P+A_89P,186</td>\n", "      <td>-615.12883</td>\n", "      <td>TNMPP</td>\n", "      <td>A_56T+A_57N+A_59M+A_60P+A_89P</td>\n", "      <td>-615.12883</td>\n", "      <td>TNMPP</td>\n", "      <td>215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>215</td>\n", "      <td>A_56A+A_57K+A_59P+A_60P+A_89R,164</td>\n", "      <td>-614.46968</td>\n", "      <td>AKPPR</td>\n", "      <td>A_56A+A_57K+A_59P+A_60P+A_89R</td>\n", "      <td>-614.46968</td>\n", "      <td>AKPPR</td>\n", "      <td>216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>216</th>\n", "      <td>216</td>\n", "      <td>A_56T+A_57R+A_59Q+A_60P+A_89P,199</td>\n", "      <td>-612.86603</td>\n", "      <td>TRQPP</td>\n", "      <td>A_56T+A_57R+A_59Q+A_60P+A_89P</td>\n", "      <td>-612.86603</td>\n", "      <td>TRQPP</td>\n", "      <td>217</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>217 rows × 8 columns</p>\n", "</div>"], "text/plain": ["    Index                               Tags       Score    Seq  \\\n", "0       0                                 WT  -666.30091  -----   \n", "1       1                     A_59L+A_60Q,59  -663.60981  --LQ-   \n", "2       2                    A_59Q+A_60Q,181  -661.23234  --QQ-   \n", "3       3  A_56H+A_57L+A_59V+A_60L+A_89W,121  -656.70887  HLVLW   \n", "4       4        A_56S+A_57W+A_60S+A_89W,138  -656.62676  SW-SW   \n", "..    ...                                ...         ...    ...   \n", "212   212   A_56P+A_57P+A_59H+A_60N+A_89G,63  -615.69023  PPHNG   \n", "213   213  A_56P+A_57A+A_59S+A_60L+A_89P,130  -615.60940  PASLP   \n", "214   214  A_56T+A_57N+A_59M+A_60P+A_89P,186  -615.12883  TNMPP   \n", "215   215  A_56A+A_57K+A_59P+A_60P+A_89R,164  -614.46968  AKPPR   \n", "216   216  A_56T+A_57R+A_59Q+A_60P+A_89P,199  -612.86603  TRQPP   \n", "\n", "                              Muts  Triad Score  Combo  Triad Rank  \n", "0                               WT   -666.30091  WYWVF           1  \n", "1                      A_59L+A_60Q   -663.60981  WYLQF           2  \n", "2                      A_59Q+A_60Q   -661.23234  WYQQF           3  \n", "3    A_56H+A_57L+A_59V+A_60L+A_89W   -656.70887  HLVLW           4  \n", "4          A_56S+A_57W+A_60S+A_89W   -656.62676  SWWSW           5  \n", "..                             ...          ...    ...         ...  \n", "212  A_56P+A_57P+A_59H+A_60N+A_89G   -615.69023  PPHNG         213  \n", "213  A_56P+A_57A+A_59S+A_60L+A_89P   -615.60940  PASLP         214  \n", "214  A_56T+A_57N+A_59M+A_60P+A_89P   -615.12883  TNMPP         215  \n", "215  A_56A+A_57K+A_59P+A_60P+A_89R   -614.46968  AKPPR         216  \n", "216  A_56T+A_57R+A_59Q+A_60P+A_89P   -612.86603  TRQPP         217  \n", "\n", "[217 rows x 8 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["all_df"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>Alignment Probability</th>\n", "      <th>Alignment Count</th>\n", "      <th>Column</th>\n", "      <th>StdArea</th>\n", "      <th>Iso1Area</th>\n", "      <th>Iso2Area</th>\n", "      <th>NormIso1</th>\n", "      <th>num_wells</th>\n", "      <th>NormIso2</th>\n", "      <th>Diff</th>\n", "      <th>EVMutation</th>\n", "      <th>EVMutation Rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FRMNY</td>\n", "      <td>7.0</td>\n", "      <td>0.624151</td>\n", "      <td>339.0</td>\n", "      <td>2.0</td>\n", "      <td>484.0</td>\n", "      <td>3398.0</td>\n", "      <td>567.0</td>\n", "      <td>7.020661</td>\n", "      <td>1</td>\n", "      <td>1.171488</td>\n", "      <td>5.849174</td>\n", "      <td>-28.982647</td>\n", "      <td>69.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SAFRY</td>\n", "      <td>2.0</td>\n", "      <td>0.755508</td>\n", "      <td>128.0</td>\n", "      <td>8.0</td>\n", "      <td>487.0</td>\n", "      <td>3778.0</td>\n", "      <td>963.0</td>\n", "      <td>7.757700</td>\n", "      <td>1</td>\n", "      <td>1.977413</td>\n", "      <td>5.780287</td>\n", "      <td>-31.842763</td>\n", "      <td>118.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GIDLY</td>\n", "      <td>165.0</td>\n", "      <td>0.086459</td>\n", "      <td>197.0</td>\n", "      <td>5.0</td>\n", "      <td>501.0</td>\n", "      <td>2485.0</td>\n", "      <td>428.0</td>\n", "      <td>4.960080</td>\n", "      <td>1</td>\n", "      <td>0.854291</td>\n", "      <td>4.105788</td>\n", "      <td>-31.299863</td>\n", "      <td>112.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TSGMY</td>\n", "      <td>28.0</td>\n", "      <td>0.244922</td>\n", "      <td>154.0</td>\n", "      <td>3.0</td>\n", "      <td>497.0</td>\n", "      <td>3058.0</td>\n", "      <td>1062.0</td>\n", "      <td>6.152918</td>\n", "      <td>1</td>\n", "      <td>2.136821</td>\n", "      <td>4.016097</td>\n", "      <td>-29.967584</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AKPPY</td>\n", "      <td>75.0</td>\n", "      <td>0.774387</td>\n", "      <td>175.0</td>\n", "      <td>7.0</td>\n", "      <td>497.0</td>\n", "      <td>2692.0</td>\n", "      <td>1163.0</td>\n", "      <td>5.416499</td>\n", "      <td>1</td>\n", "      <td>2.340040</td>\n", "      <td>3.076459</td>\n", "      <td>-35.509484</td>\n", "      <td>187.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>VCRAS</td>\n", "      <td>190.0</td>\n", "      <td>0.476841</td>\n", "      <td>297.0</td>\n", "      <td>4.0</td>\n", "      <td>501.0</td>\n", "      <td>200.0</td>\n", "      <td>3415.0</td>\n", "      <td>0.399202</td>\n", "      <td>1</td>\n", "      <td>6.816367</td>\n", "      <td>-6.417166</td>\n", "      <td>-29.500047</td>\n", "      <td>78.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>HDNMN</td>\n", "      <td>173.0</td>\n", "      <td>0.155533</td>\n", "      <td>277.0</td>\n", "      <td>4.0</td>\n", "      <td>501.0</td>\n", "      <td>148.0</td>\n", "      <td>3378.0</td>\n", "      <td>0.295409</td>\n", "      <td>1</td>\n", "      <td>6.742515</td>\n", "      <td>-6.447106</td>\n", "      <td>-29.217710</td>\n", "      <td>72.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>PCLTD</td>\n", "      <td>251.0</td>\n", "      <td>0.717479</td>\n", "      <td>513.0</td>\n", "      <td>1.0</td>\n", "      <td>506.0</td>\n", "      <td>88.0</td>\n", "      <td>3513.0</td>\n", "      <td>0.173913</td>\n", "      <td>1</td>\n", "      <td>6.942688</td>\n", "      <td>-6.768775</td>\n", "      <td>-38.626022</td>\n", "      <td>216.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>ACSEK</td>\n", "      <td>60.0</td>\n", "      <td>0.655179</td>\n", "      <td>483.0</td>\n", "      <td>8.0</td>\n", "      <td>490.0</td>\n", "      <td>243.0</td>\n", "      <td>3579.0</td>\n", "      <td>0.495918</td>\n", "      <td>1</td>\n", "      <td>7.304082</td>\n", "      <td>-6.808163</td>\n", "      <td>-34.614352</td>\n", "      <td>172.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>TSGRE</td>\n", "      <td>193.0</td>\n", "      <td>0.001915</td>\n", "      <td>556.0</td>\n", "      <td>2.0</td>\n", "      <td>490.0</td>\n", "      <td>107.0</td>\n", "      <td>3496.0</td>\n", "      <td>0.218367</td>\n", "      <td>1</td>\n", "      <td>7.134694</td>\n", "      <td>-6.916327</td>\n", "      <td>-35.417786</td>\n", "      <td>185.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>216 rows × 14 columns</p>\n", "</div>"], "text/plain": ["     Combo  Unnamed: 0  Alignment Probability  Alignment Count  Column  \\\n", "0    FRMNY         7.0               0.624151            339.0     2.0   \n", "1    SAFRY         2.0               0.755508            128.0     8.0   \n", "2    GIDLY       165.0               0.086459            197.0     5.0   \n", "3    TSGMY        28.0               0.244922            154.0     3.0   \n", "4    AKPPY        75.0               0.774387            175.0     7.0   \n", "..     ...         ...                    ...              ...     ...   \n", "211  VCRAS       190.0               0.476841            297.0     4.0   \n", "212  HDNMN       173.0               0.155533            277.0     4.0   \n", "213  PCLTD       251.0               0.717479            513.0     1.0   \n", "214  ACSEK        60.0               0.655179            483.0     8.0   \n", "215  TSGRE       193.0               0.001915            556.0     2.0   \n", "\n", "     StdArea  Iso1Area  Iso2Area  NormIso1  num_wells  NormIso2      Diff  \\\n", "0      484.0    3398.0     567.0  7.020661          1  1.171488  5.849174   \n", "1      487.0    3778.0     963.0  7.757700          1  1.977413  5.780287   \n", "2      501.0    2485.0     428.0  4.960080          1  0.854291  4.105788   \n", "3      497.0    3058.0    1062.0  6.152918          1  2.136821  4.016097   \n", "4      497.0    2692.0    1163.0  5.416499          1  2.340040  3.076459   \n", "..       ...       ...       ...       ...        ...       ...       ...   \n", "211    501.0     200.0    3415.0  0.399202          1  6.816367 -6.417166   \n", "212    501.0     148.0    3378.0  0.295409          1  6.742515 -6.447106   \n", "213    506.0      88.0    3513.0  0.173913          1  6.942688 -6.768775   \n", "214    490.0     243.0    3579.0  0.495918          1  7.304082 -6.808163   \n", "215    490.0     107.0    3496.0  0.218367          1  7.134694 -6.916327   \n", "\n", "     EVMutation  EVMutation Rank  \n", "0    -28.982647             69.0  \n", "1    -31.842763            118.0  \n", "2    -31.299863            112.0  \n", "3    -29.967584             90.0  \n", "4    -35.509484            187.0  \n", "..          ...              ...  \n", "211  -29.500047             78.0  \n", "212  -29.217710             72.0  \n", "213  -38.626022            216.0  \n", "214  -34.614352            172.0  \n", "215  -35.417786            185.0  \n", "\n", "[216 rows x 14 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["merged = pd.read_csv('/disk1/jyang4/repos/data/Pgb_fitness.csv')\n", "merged"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>Alignment Probability</th>\n", "      <th>Alignment Count</th>\n", "      <th>Column</th>\n", "      <th>StdArea</th>\n", "      <th>Iso1Area</th>\n", "      <th>Iso2Area</th>\n", "      <th>NormIso1</th>\n", "      <th>num_wells</th>\n", "      <th>NormIso2</th>\n", "      <th>Diff</th>\n", "      <th>EVMutation</th>\n", "      <th>EVMutation Rank</th>\n", "      <th>Triad Score</th>\n", "      <th>Triad Rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FRMNY</td>\n", "      <td>7.0</td>\n", "      <td>0.624151</td>\n", "      <td>339.0</td>\n", "      <td>2.0</td>\n", "      <td>484.0</td>\n", "      <td>3398.0</td>\n", "      <td>567.0</td>\n", "      <td>7.020661</td>\n", "      <td>1</td>\n", "      <td>1.171488</td>\n", "      <td>5.849174</td>\n", "      <td>-28.982647</td>\n", "      <td>69.0</td>\n", "      <td>-648.97722</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SAFRY</td>\n", "      <td>2.0</td>\n", "      <td>0.755508</td>\n", "      <td>128.0</td>\n", "      <td>8.0</td>\n", "      <td>487.0</td>\n", "      <td>3778.0</td>\n", "      <td>963.0</td>\n", "      <td>7.757700</td>\n", "      <td>1</td>\n", "      <td>1.977413</td>\n", "      <td>5.780287</td>\n", "      <td>-31.842763</td>\n", "      <td>118.0</td>\n", "      <td>-646.67160</td>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GIDLY</td>\n", "      <td>165.0</td>\n", "      <td>0.086459</td>\n", "      <td>197.0</td>\n", "      <td>5.0</td>\n", "      <td>501.0</td>\n", "      <td>2485.0</td>\n", "      <td>428.0</td>\n", "      <td>4.960080</td>\n", "      <td>1</td>\n", "      <td>0.854291</td>\n", "      <td>4.105788</td>\n", "      <td>-31.299863</td>\n", "      <td>112.0</td>\n", "      <td>-642.32678</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TSGMY</td>\n", "      <td>28.0</td>\n", "      <td>0.244922</td>\n", "      <td>154.0</td>\n", "      <td>3.0</td>\n", "      <td>497.0</td>\n", "      <td>3058.0</td>\n", "      <td>1062.0</td>\n", "      <td>6.152918</td>\n", "      <td>1</td>\n", "      <td>2.136821</td>\n", "      <td>4.016097</td>\n", "      <td>-29.967584</td>\n", "      <td>90.0</td>\n", "      <td>-645.07324</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AKPPY</td>\n", "      <td>75.0</td>\n", "      <td>0.774387</td>\n", "      <td>175.0</td>\n", "      <td>7.0</td>\n", "      <td>497.0</td>\n", "      <td>2692.0</td>\n", "      <td>1163.0</td>\n", "      <td>5.416499</td>\n", "      <td>1</td>\n", "      <td>2.340040</td>\n", "      <td>3.076459</td>\n", "      <td>-35.509484</td>\n", "      <td>187.0</td>\n", "      <td>-618.95309</td>\n", "      <td>207</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>VCRAS</td>\n", "      <td>190.0</td>\n", "      <td>0.476841</td>\n", "      <td>297.0</td>\n", "      <td>4.0</td>\n", "      <td>501.0</td>\n", "      <td>200.0</td>\n", "      <td>3415.0</td>\n", "      <td>0.399202</td>\n", "      <td>1</td>\n", "      <td>6.816367</td>\n", "      <td>-6.417166</td>\n", "      <td>-29.500047</td>\n", "      <td>78.0</td>\n", "      <td>-641.07042</td>\n", "      <td>115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>HDNMN</td>\n", "      <td>173.0</td>\n", "      <td>0.155533</td>\n", "      <td>277.0</td>\n", "      <td>4.0</td>\n", "      <td>501.0</td>\n", "      <td>148.0</td>\n", "      <td>3378.0</td>\n", "      <td>0.295409</td>\n", "      <td>1</td>\n", "      <td>6.742515</td>\n", "      <td>-6.447106</td>\n", "      <td>-29.217710</td>\n", "      <td>72.0</td>\n", "      <td>-642.37494</td>\n", "      <td>94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>PCLTD</td>\n", "      <td>251.0</td>\n", "      <td>0.717479</td>\n", "      <td>513.0</td>\n", "      <td>1.0</td>\n", "      <td>506.0</td>\n", "      <td>88.0</td>\n", "      <td>3513.0</td>\n", "      <td>0.173913</td>\n", "      <td>1</td>\n", "      <td>6.942688</td>\n", "      <td>-6.768775</td>\n", "      <td>-38.626022</td>\n", "      <td>216.0</td>\n", "      <td>-629.35627</td>\n", "      <td>187</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>ACSEK</td>\n", "      <td>60.0</td>\n", "      <td>0.655179</td>\n", "      <td>483.0</td>\n", "      <td>8.0</td>\n", "      <td>490.0</td>\n", "      <td>243.0</td>\n", "      <td>3579.0</td>\n", "      <td>0.495918</td>\n", "      <td>1</td>\n", "      <td>7.304082</td>\n", "      <td>-6.808163</td>\n", "      <td>-34.614352</td>\n", "      <td>172.0</td>\n", "      <td>-639.54535</td>\n", "      <td>132</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>TSGRE</td>\n", "      <td>193.0</td>\n", "      <td>0.001915</td>\n", "      <td>556.0</td>\n", "      <td>2.0</td>\n", "      <td>490.0</td>\n", "      <td>107.0</td>\n", "      <td>3496.0</td>\n", "      <td>0.218367</td>\n", "      <td>1</td>\n", "      <td>7.134694</td>\n", "      <td>-6.916327</td>\n", "      <td>-35.417786</td>\n", "      <td>185.0</td>\n", "      <td>-639.24294</td>\n", "      <td>135</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>216 rows × 16 columns</p>\n", "</div>"], "text/plain": ["     Combo  Unnamed: 0  Alignment Probability  Alignment Count  Column  \\\n", "0    FRMNY         7.0               0.624151            339.0     2.0   \n", "1    SAFRY         2.0               0.755508            128.0     8.0   \n", "2    GIDLY       165.0               0.086459            197.0     5.0   \n", "3    TSGMY        28.0               0.244922            154.0     3.0   \n", "4    AKPPY        75.0               0.774387            175.0     7.0   \n", "..     ...         ...                    ...              ...     ...   \n", "211  VCRAS       190.0               0.476841            297.0     4.0   \n", "212  HDNMN       173.0               0.155533            277.0     4.0   \n", "213  PCLTD       251.0               0.717479            513.0     1.0   \n", "214  ACSEK        60.0               0.655179            483.0     8.0   \n", "215  TSGRE       193.0               0.001915            556.0     2.0   \n", "\n", "     StdArea  Iso1Area  Iso2Area  NormIso1  num_wells  NormIso2      Diff  \\\n", "0      484.0    3398.0     567.0  7.020661          1  1.171488  5.849174   \n", "1      487.0    3778.0     963.0  7.757700          1  1.977413  5.780287   \n", "2      501.0    2485.0     428.0  4.960080          1  0.854291  4.105788   \n", "3      497.0    3058.0    1062.0  6.152918          1  2.136821  4.016097   \n", "4      497.0    2692.0    1163.0  5.416499          1  2.340040  3.076459   \n", "..       ...       ...       ...       ...        ...       ...       ...   \n", "211    501.0     200.0    3415.0  0.399202          1  6.816367 -6.417166   \n", "212    501.0     148.0    3378.0  0.295409          1  6.742515 -6.447106   \n", "213    506.0      88.0    3513.0  0.173913          1  6.942688 -6.768775   \n", "214    490.0     243.0    3579.0  0.495918          1  7.304082 -6.808163   \n", "215    490.0     107.0    3496.0  0.218367          1  7.134694 -6.916327   \n", "\n", "     EVMutation  EVMutation Rank  Triad Score  Triad Rank  \n", "0    -28.982647             69.0   -648.97722          33  \n", "1    -31.842763            118.0   -646.67160          48  \n", "2    -31.299863            112.0   -642.32678          97  \n", "3    -29.967584             90.0   -645.07324          60  \n", "4    -35.509484            187.0   -618.95309         207  \n", "..          ...              ...          ...         ...  \n", "211  -29.500047             78.0   -641.07042         115  \n", "212  -29.217710             72.0   -642.37494          94  \n", "213  -38.626022            216.0   -629.35627         187  \n", "214  -34.614352            172.0   -639.54535         132  \n", "215  -35.417786            185.0   -639.24294         135  \n", "\n", "[216 rows x 16 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_new = merged.merge(all_df[['Combo', 'Triad Score', 'Triad Rank']],on='Combo')\n", "merged_new"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["merged_new.to_csv('ParPgb_finess_all.csv', index=None)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# df = pd.read_csv('fitness_all.csv')\n", "# initial = df[df['round'].isin(['initial', 'parent'])]\n", "# initial.to_csv('fitness_round1_training.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.7.3 ('mlde2')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.1.-1"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "76080dab29a11557eff43621d14f30d08fac3a4367678bd8b58214f74f029254"}}}, "nbformat": 4, "nbformat_minor": 2}