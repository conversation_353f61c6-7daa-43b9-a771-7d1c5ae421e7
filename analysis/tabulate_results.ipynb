{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import torch\n", "from datetime import datetime\n", "import glob\n", "import os\n", "import math\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"scrolled": true}, "outputs": [], "source": ["def index2regret(indices, y):\n", "    \"\"\"\n", "    Converts list of queried indices to regret (difference between the max value in the deisgn space and the max queired value)\n", "    \"\"\"\n", "    indices = np.array(indices, dtype=int)\n", "    regret = torch.zeros((indices.shape[0], indices.shape[1]))\n", "    for i in range(indices.shape[0]):\n", "        for j in range(indices.shape[1]):\n", "            regret[i, j] = 1 - y[indices[i, :j+1]].max()\n", "    return regret"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"scrolled": false}, "outputs": [], "source": ["def load_tensors(subdir):\n", "    \"\"\"\n", "    loads al indices from a directory and converts them to regret values\n", "    \"\"\"\n", "    tensors = sorted(glob.glob(subdir + '/*indices*.pt'))\n", "        \n", "    tests = {}\n", "    print('Models not included/not over budget yet:\\n')\n", "\n", "    for tensor in tensors:\n", "        num = int(tensor.split('indices')[0].split('_')[-1])\n", "\n", "        if '.pt' in tensor and 'state_dict' not in tensor:\n", "            first = False\n", "            if \"Random\" not in os.path.basename(tensor):\n", "                nm = os.path.basename(tensor).split('_')[0] + '_' + os.path.basename(tensor).split('_')[1]\n", "            else:     \n", "                nm = os.path.basename(tensor).split('_')[0]\n", "\n", "            t = torch.load(tensor).cpu().detach()\n", "            t = torch.reshape(t, (1, -1))\n", "\n", "            lim = 480\n", "            if t.size(-1) < lim:\n", "                if 'indices.pt' in tensor:\n", "                    print(tensor.split('/')[-1])\n", "                continue\n", "            if nm in tests.keys():\n", "                d = tests[nm]\n", "            else:\n", "                d = {}\n", "                tests[nm] = d\n", "\n", "            dtype = os.path.basename(tensor).split('_')[-1].split('.')[0]\n", "            dtype = ''.join([i for i in dtype if not i.isdigit()])\n", "            if dtype in d.keys():\n", "                arr = d[dtype]\n", "            else:\n", "                arr = t\n", "                d[dtype] = arr\n", "                first = True\n", "            if first:\n", "                pass\n", "            elif t.size(-1) < arr.size(-1):\n", "                arr = torch.cat((arr[:,:t.size(-1)], t), 0)\n", "            else:\n", "                arr = torch.cat((arr, t[:,:arr.size(-1)]), 0)\n", "            d[dtype] = arr\n", "            tests[nm] = d\n", "\n", "    print('\\nModels included:\\n')\n", "    batch = {}\n", "    budget, total= math.inf, math.inf\n", "    for key in tests.keys():\n", "        print(key)\n", "        num_runs = -1\n", "        for dtype in tests[key].keys():\n", "            t = tests[key][dtype]\n", "            t = index2regret(t, y)\n", "            \n", "            if 'indices' == dtype and t.size(-1) != 0:\n", "                if t.size(-1) < budget:\n", "                    budget = t.size(-1)\n", "                num_runs = t.size(0)\n", "            elif 'y' in dtype and t.size(-1) < total and t.size(-1) != 0:\n", "                total = t.size(-1)\n", "            \n", "            sd, mean = torch.std_mean(t, 0)\n", "            #calculate the fraction of t that equals 0 regret along the first dimension\n", "            #mean = torch.where(t == 0, torch.tensor(1.), torch.tensor(0.)).sum(0) / t.size(0)\n", "\n", "            sem = sd / (t.size(0)**.5)\n", "            if dtype in batch.keys():\n", "                d = batch[dtype]\n", "            else:\n", "                d = {}\n", "                batch[dtype] = d\n", "\n", "            d[key] = (mean, sem)\n", "\n", "            batch[dtype] = d\n", "        print(\"Runs: {}\".format(num_runs))\n", "    print(batch.keys())\n", "    print('Budget: {}'.format(budget))\n", "    print('Total queries (incl. init): {}'.format(budget))\n", "\n", "    return batch, budget"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": false}, "outputs": [], "source": ["def tabulate_regret(df, tests, budget, subdir, randregret=None):\n", "    \"\"\"\n", "    Tabulates loaded regret values into an organized dataframe.\n", "    \"\"\"\n", "    names = []\n", "    queries = np.arange(budget) + 1\n", "    \n", "    for name in sorted(tests.keys()):\n", "        names.append(name)\n", "\n", "        mean, sem = tests[name]\n", "        mean = 1 - mean\n", "        if mean.size(0) < budget:\n", "            names.pop()\n", "            continue\n", "        if mean.size(0) > budget:\n", "            mean = mean[:budget]\n", "            sem = sem[:budget]\n", "\n", "        protein = subdir.split('/')[-2]\n", "        encoding = subdir.split('/')[-1]\n", "\n", "        if 'Random' in name:\n", "            encoding = 'Random'\n", "            model = 'Random'\n", "            acquisition = 'Random'\n", "        else:\n", "            names = name.split('-')\n", "            model = names[0]\n", "            acquisition = names[-2]\n", "\n", "        for timestep, single_mean, single_std in zip(queries, np.array(mean), np.array(sem)):\n", "            df.loc[len(df.index)] = [protein, encoding, model, acquisition, timestep, single_mean, single_std]\n", "    return df"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Models not included/not over budget yet:\n", "\n", "\n", "Models included:\n", "\n", "BOOSTING_ENSEMBLE-DO-0-RBF-GREEDY-[16, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-TS-[16, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-UCB-[16, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-GREEDY-[8, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-TS-[8, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-UCB-[8, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-GREEDY-[8, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-TS-[8, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-UCB-[8, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-GREEDY-[16, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-TS-[16, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-UCB-[16, 1]\n", "Runs: 70\n", "Random\n", "Runs: 70\n", "dict_keys(['indices'])\n", "Budget: 480\n", "Total queries (incl. init): 480\n", "Models not included/not over budget yet:\n", "\n", "\n", "Models included:\n", "\n", "BOOSTING_ENSEMBLE-DO-0-RBF-GREEDY-[76, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-TS-[76, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-UCB-[76, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-GREEDY-[30, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-TS-[30, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-UCB-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-GREEDY-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-TS-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-UCB-[30, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-GREEDY-[76, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-TS-[76, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-UCB-[76, 1]\n", "Runs: 70\n", "Random\n", "Runs: 70\n", "dict_keys(['indices'])\n", "Budget: 480\n", "Total queries (incl. init): 480\n", "Models not included/not over budget yet:\n", "\n", "\n", "Models included:\n", "\n", "BOOSTING_ENSEMBLE-DO-0-RBF-GREEDY-[80, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-TS-[80, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-UCB-[80, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-GREEDY-[30, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-TS-[30, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-UCB-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-GREEDY-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-TS-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-UCB-[30, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-GREEDY-[80, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-TS-[80, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-UCB-[80, 1]\n", "Runs: 70\n", "Random\n", "Runs: 70\n", "dict_keys(['indices'])\n", "Budget: 480\n", "Total queries (incl. init): 480\n", "Models not included/not over budget yet:\n", "\n", "\n", "Models included:\n", "\n", "BOOSTING_ENSEMBLE-DO-0-RBF-GREEDY-[5120, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-TS-[5120, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-UCB-[5120, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-GREEDY-[50, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-TS-[50, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-UCB-[50, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-GREEDY-[50, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-TS-[50, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-UCB-[50, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-GREEDY-[5120, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-TS-[5120, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-UCB-[5120, 1]\n", "Runs: 70\n", "Random\n", "Runs: 70\n", "dict_keys(['indices'])\n", "Budget: 480\n", "Total queries (incl. init): 480\n", "Models not included/not over budget yet:\n", "\n", "\n", "Models included:\n", "\n", "BOOSTING_ENSEMBLE-DO-0-RBF-GREEDY-[16, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-TS-[16, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-UCB-[16, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-GREEDY-[8, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-TS-[8, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-UCB-[8, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-GREEDY-[8, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-TS-[8, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-UCB-[8, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-GREEDY-[16, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-TS-[16, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-UCB-[16, 1]\n", "Runs: 70\n", "Random\n", "Runs: 70\n", "dict_keys(['indices'])\n", "Budget: 480\n", "Total queries (incl. init): 480\n", "Models not included/not over budget yet:\n", "\n", "\n", "Models included:\n", "\n", "BOOSTING_ENSEMBLE-DO-0-RBF-GREEDY-[76, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-TS-[76, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-UCB-[76, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-GREEDY-[30, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-TS-[30, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-UCB-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-GREEDY-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-TS-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-UCB-[30, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-GREEDY-[76, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-TS-[76, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-UCB-[76, 1]\n", "Runs: 70\n", "Random\n", "Runs: 70\n", "dict_keys(['indices'])\n", "Budget: 480\n", "Total queries (incl. init): 480\n", "Models not included/not over budget yet:\n", "\n", "\n", "Models included:\n", "\n", "BOOSTING_ENSEMBLE-DO-0-RBF-GREEDY-[80, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-TS-[80, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-UCB-[80, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-GREEDY-[30, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-TS-[30, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-UCB-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-GREEDY-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-TS-[30, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-UCB-[30, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-GREEDY-[80, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-TS-[80, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-UCB-[80, 1]\n", "Runs: 70\n", "Random\n", "Runs: 70\n", "dict_keys(['indices'])\n", "Budget: 480\n", "Total queries (incl. init): 480\n", "Models not included/not over budget yet:\n", "\n", "\n", "Models included:\n", "\n", "BOOSTING_ENSEMBLE-DO-0-RBF-GREEDY-[5120, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-TS-[5120, 1]\n", "Runs: 70\n", "BOOSTING_ENSEMBLE-DO-0-RBF-UCB-[5120, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-GREEDY-[50, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-TS-[50, 1]\n", "Runs: 70\n", "DKL_BOTORCH-DO-0-RBF-UCB-[50, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-GREEDY-[50, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-TS-[50, 1]\n", "Runs: 70\n", "DNN_ENSEMBLE-DO-0-RBF-UCB-[50, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-GREEDY-[5120, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-TS-[5120, 1]\n", "Runs: 70\n", "GP_BOTORCH-DO-0-RBF-UCB-[5120, 1]\n", "Runs: 70\n", "Random\n", "Runs: 70\n", "dict_keys(['indices'])\n", "Budget: 480\n", "Total queries (incl. init): 480\n"]}], "source": ["#Loop over different datasets and encodings\n", "#tabulate all max fitness values into one dataframe\n", "df = pd.DataFrame(columns=['Protein', 'Encoding', 'Model', 'Acquisition', 'Timestep', 'Mean', 'Std'])\n", "\n", "for protein in ['GB1', 'TrpB']:\n", "    for encoding in ['AA', 'georgiev', 'onehot', 'ESM2']:\n", "        subdir =  '../results/5x96_simulations/' + protein + '/' + encoding #384+96_baseline #5x96_simulations\n", "        fitness_df = pd.read_csv('../data/' + protein + '/fitness.csv')\n", "        y = fitness_df['fitness'].values\n", "        y = y/y.max()\n", "       \n", "        batch, budget = load_tensors(subdir)\n", "        df = tabulate_regret(df, batch['indices'], budget, subdir, randregret=None)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Encoding</th>\n", "      <th>Model</th>\n", "      <th>Acquisition</th>\n", "      <th>Timestep</th>\n", "      <th>Mean</th>\n", "      <th>Std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>GB1</td>\n", "      <td>AA</td>\n", "      <td>Boosting Ensemble</td>\n", "      <td>GREEDY</td>\n", "      <td>1</td>\n", "      <td>0.010112</td>\n", "      <td>0.004871</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>GB1</td>\n", "      <td>AA</td>\n", "      <td>Boosting Ensemble</td>\n", "      <td>GREEDY</td>\n", "      <td>2</td>\n", "      <td>0.021386</td>\n", "      <td>0.007114</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GB1</td>\n", "      <td>AA</td>\n", "      <td>Boosting Ensemble</td>\n", "      <td>GREEDY</td>\n", "      <td>3</td>\n", "      <td>0.043239</td>\n", "      <td>0.011129</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>GB1</td>\n", "      <td>AA</td>\n", "      <td>Boosting Ensemble</td>\n", "      <td>GREEDY</td>\n", "      <td>4</td>\n", "      <td>0.046141</td>\n", "      <td>0.011136</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GB1</td>\n", "      <td>AA</td>\n", "      <td>Boosting Ensemble</td>\n", "      <td>GREEDY</td>\n", "      <td>5</td>\n", "      <td>0.050109</td>\n", "      <td>0.011454</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49435</th>\n", "      <td>TrpB</td>\n", "      <td>ESM2</td>\n", "      <td>GP</td>\n", "      <td>UCB</td>\n", "      <td>476</td>\n", "      <td>0.616093</td>\n", "      <td>0.004441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49436</th>\n", "      <td>TrpB</td>\n", "      <td>ESM2</td>\n", "      <td>GP</td>\n", "      <td>UCB</td>\n", "      <td>477</td>\n", "      <td>0.616093</td>\n", "      <td>0.004441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49437</th>\n", "      <td>TrpB</td>\n", "      <td>ESM2</td>\n", "      <td>GP</td>\n", "      <td>UCB</td>\n", "      <td>478</td>\n", "      <td>0.616093</td>\n", "      <td>0.004441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49438</th>\n", "      <td>TrpB</td>\n", "      <td>ESM2</td>\n", "      <td>GP</td>\n", "      <td>UCB</td>\n", "      <td>479</td>\n", "      <td>0.616093</td>\n", "      <td>0.004441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49439</th>\n", "      <td>TrpB</td>\n", "      <td>ESM2</td>\n", "      <td>GP</td>\n", "      <td>UCB</td>\n", "      <td>480</td>\n", "      <td>0.616093</td>\n", "      <td>0.004441</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>47040 rows × 7 columns</p>\n", "</div>"], "text/plain": ["      Protein Encoding              Model Acquisition  Timestep      Mean  \\\n", "0         GB1       AA  Boosting Ensemble      GREEDY         1  0.010112   \n", "1         GB1       AA  Boosting Ensemble      GREEDY         2  0.021386   \n", "2         GB1       AA  Boosting Ensemble      GREEDY         3  0.043239   \n", "3         GB1       AA  Boosting Ensemble      GREEDY         4  0.046141   \n", "4         GB1       AA  Boosting Ensemble      GREEDY         5  0.050109   \n", "...       ...      ...                ...         ...       ...       ...   \n", "49435    TrpB     ESM2                 GP         UCB       476  0.616093   \n", "49436    TrpB     ESM2                 GP         UCB       477  0.616093   \n", "49437    TrpB     ESM2                 GP         UCB       478  0.616093   \n", "49438    TrpB     ESM2                 GP         UCB       479  0.616093   \n", "49439    TrpB     ESM2                 GP         UCB       480  0.616093   \n", "\n", "            Std  \n", "0      0.004871  \n", "1      0.007114  \n", "2      0.011129  \n", "3      0.011136  \n", "4      0.011454  \n", "...         ...  \n", "49435  0.004441  \n", "49436  0.004441  \n", "49437  0.004441  \n", "49438  0.004441  \n", "49439  0.004441  \n", "\n", "[47040 rows x 7 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#clean up the results and save\n", "df = df.drop_duplicates(subset=['Protein', 'Encoding', 'Model', 'Acquisition', 'Timestep'], keep='first')\n", "df['Model'] = df['Model'].replace('BOOSTING_ENSEMBLE', 'Boosting Ensemble')\n", "df['Model'] = df['Model'].replace('GP_BOTORCH', 'GP')\n", "df['Model'] = df['Model'].replace('DNN_ENSEMBLE', 'DNN Ensemble')\n", "df['Model'] = df['Model'].replace('DKL_BOTORCH', 'DKL')\n", "df['Acquisition'] = df['Acquisition'].replace('Random', 'GREEDY')\n", "df.to_csv('all_results.csv', index=False)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 4}