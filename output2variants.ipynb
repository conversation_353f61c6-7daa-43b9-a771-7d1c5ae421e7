{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>756239</th>\n", "      <td>FRMNY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2401899</th>\n", "      <td>SAFRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>856999</th>\n", "      <td>GIDLY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2682219</th>\n", "      <td>TSGMY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2652259</th>\n", "      <td>TNMPY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>705039</th>\n", "      <td>FKDNY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>641039</th>\n", "      <td>FADNY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2678639</th>\n", "      <td>TRTNY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>753859</th>\n", "      <td>FRFPY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>649799</th>\n", "      <td>FCFLY</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>306 rows × 1 columns</p>\n", "</div>"], "text/plain": ["         Combo\n", "756239   FRMNY\n", "2401899  SAFRY\n", "856999   GIDLY\n", "2682219  TSGMY\n", "2652259  TNMPY\n", "...        ...\n", "705039   FKDNY\n", "641039   FADNY\n", "2678639  TRTNY\n", "753859   FRFPY\n", "649799   FCFLY\n", "\n", "[306 rows x 1 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#load the combos and index them based on the predictions\n", "combo_df = pd.read_csv('data/ParPgb/all_combos.csv')\n", "indices = torch.load('results/ParPgb_production/round1/DNN_ENSEMBLE-DO-0-RBF-TS-[30, 1]_1indices.pt') #replace this with any other output that ends in indices.pt\n", "selected = combo_df.iloc[indices]\n", "selected"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>PlateNormIso1</th>\n", "      <th>PlateNormIso2</th>\n", "      <th>Diff</th>\n", "      <th>round</th>\n", "      <th>yield1</th>\n", "      <th>yield2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FRMNY</td>\n", "      <td>7.020661</td>\n", "      <td>1.171488</td>\n", "      <td>4.586441</td>\n", "      <td>0.765307</td>\n", "      <td>3.821134</td>\n", "      <td>initial</td>\n", "      <td>63.689822</td>\n", "      <td>10.023846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SAFRY</td>\n", "      <td>7.757700</td>\n", "      <td>1.977413</td>\n", "      <td>5.067932</td>\n", "      <td>1.291800</td>\n", "      <td>3.776133</td>\n", "      <td>initial</td>\n", "      <td>70.447102</td>\n", "      <td>17.547888</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GIDLY</td>\n", "      <td>4.960080</td>\n", "      <td>0.854291</td>\n", "      <td>3.449043</td>\n", "      <td>0.594040</td>\n", "      <td>2.855002</td>\n", "      <td>initial</td>\n", "      <td>44.798116</td>\n", "      <td>7.062533</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TSGMY</td>\n", "      <td>6.152918</td>\n", "      <td>2.136821</td>\n", "      <td>4.019563</td>\n", "      <td>1.395937</td>\n", "      <td>2.623626</td>\n", "      <td>initial</td>\n", "      <td>55.734223</td>\n", "      <td>19.036107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TNMPY</td>\n", "      <td>3.694215</td>\n", "      <td>0.758264</td>\n", "      <td>3.260248</td>\n", "      <td>0.669190</td>\n", "      <td>2.591058</td>\n", "      <td>initial</td>\n", "      <td>33.192484</td>\n", "      <td>6.166034</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>LQSGA</td>\n", "      <td>0.582278</td>\n", "      <td>5.748945</td>\n", "      <td>0.513877</td>\n", "      <td>5.073604</td>\n", "      <td>-4.559727</td>\n", "      <td>initial</td>\n", "      <td>4.661804</td>\n", "      <td>52.758559</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>QYKGD</td>\n", "      <td>0.242081</td>\n", "      <td>5.511312</td>\n", "      <td>0.213644</td>\n", "      <td>4.863887</td>\n", "      <td>-4.650243</td>\n", "      <td>initial</td>\n", "      <td>1.542829</td>\n", "      <td>50.540041</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>PCLTD</td>\n", "      <td>0.173913</td>\n", "      <td>6.942688</td>\n", "      <td>0.120283</td>\n", "      <td>4.801761</td>\n", "      <td>-4.681477</td>\n", "      <td>initial</td>\n", "      <td>0.917851</td>\n", "      <td>63.903228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>NNVER</td>\n", "      <td>0.208251</td>\n", "      <td>5.593320</td>\n", "      <td>0.183788</td>\n", "      <td>4.936261</td>\n", "      <td>-4.752473</td>\n", "      <td>initial</td>\n", "      <td>1.232670</td>\n", "      <td>51.305660</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>TSGRE</td>\n", "      <td>0.218367</td>\n", "      <td>7.134694</td>\n", "      <td>0.151029</td>\n", "      <td>4.934558</td>\n", "      <td>-4.783529</td>\n", "      <td>initial</td>\n", "      <td>1.325414</td>\n", "      <td>65.695779</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>216 rows × 9 columns</p>\n", "</div>"], "text/plain": ["     Combo  NormIso1  NormIso2  PlateNormIso1  PlateNormIso2      Diff  \\\n", "0    FRMNY  7.020661  1.171488       4.586441       0.765307  3.821134   \n", "1    SAFRY  7.757700  1.977413       5.067932       1.291800  3.776133   \n", "2    GIDLY  4.960080  0.854291       3.449043       0.594040  2.855002   \n", "3    TSGMY  6.152918  2.136821       4.019563       1.395937  2.623626   \n", "4    TNMPY  3.694215  0.758264       3.260248       0.669190  2.591058   \n", "..     ...       ...       ...            ...            ...       ...   \n", "211  LQSGA  0.582278  5.748945       0.513877       5.073604 -4.559727   \n", "212  QYKGD  0.242081  5.511312       0.213644       4.863887 -4.650243   \n", "213  PCLTD  0.173913  6.942688       0.120283       4.801761 -4.681477   \n", "214  NNVER  0.208251  5.593320       0.183788       4.936261 -4.752473   \n", "215  TSGRE  0.218367  7.134694       0.151029       4.934558 -4.783529   \n", "\n", "       round     yield1     yield2  \n", "0    initial  63.689822  10.023846  \n", "1    initial  70.447102  17.547888  \n", "2    initial  44.798116   7.062533  \n", "3    initial  55.734223  19.036107  \n", "4    initial  33.192484   6.166034  \n", "..       ...        ...        ...  \n", "211  initial   4.661804  52.758559  \n", "212  initial   1.542829  50.540041  \n", "213  initial   0.917851  63.903228  \n", "214  initial   1.232670  51.305660  \n", "215  initial   1.325414  65.695779  \n", "\n", "[216 rows x 9 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["#load original training data\n", "training_df = pd.read_csv('data/ParPgb/fitness_round1_training.csv')\n", "training_df"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>PlateNormIso1</th>\n", "      <th>PlateNormIso2</th>\n", "      <th>Diff</th>\n", "      <th>round</th>\n", "      <th>yield1</th>\n", "      <th>yield2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FRMNY</td>\n", "      <td>7.020661</td>\n", "      <td>1.171488</td>\n", "      <td>4.586441</td>\n", "      <td>0.765307</td>\n", "      <td>3.821134</td>\n", "      <td>initial</td>\n", "      <td>63.689822</td>\n", "      <td>10.023846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SAFRY</td>\n", "      <td>7.757700</td>\n", "      <td>1.977413</td>\n", "      <td>5.067932</td>\n", "      <td>1.291800</td>\n", "      <td>3.776133</td>\n", "      <td>initial</td>\n", "      <td>70.447102</td>\n", "      <td>17.547888</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GIDLY</td>\n", "      <td>4.960080</td>\n", "      <td>0.854291</td>\n", "      <td>3.449043</td>\n", "      <td>0.594040</td>\n", "      <td>2.855002</td>\n", "      <td>initial</td>\n", "      <td>44.798116</td>\n", "      <td>7.062533</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TSGMY</td>\n", "      <td>6.152918</td>\n", "      <td>2.136821</td>\n", "      <td>4.019563</td>\n", "      <td>1.395937</td>\n", "      <td>2.623626</td>\n", "      <td>initial</td>\n", "      <td>55.734223</td>\n", "      <td>19.036107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TNMPY</td>\n", "      <td>3.694215</td>\n", "      <td>0.758264</td>\n", "      <td>3.260248</td>\n", "      <td>0.669190</td>\n", "      <td>2.591058</td>\n", "      <td>initial</td>\n", "      <td>33.192484</td>\n", "      <td>6.166034</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>301</th>\n", "      <td>FKDNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>302</th>\n", "      <td>FADNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>TRTNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td>FRFPY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>305</th>\n", "      <td>FCFLY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>306 rows × 9 columns</p>\n", "</div>"], "text/plain": ["     Combo  NormIso1  NormIso2  PlateNormIso1  PlateNormIso2      Diff  \\\n", "0    FRMNY  7.020661  1.171488       4.586441       0.765307  3.821134   \n", "1    SAFRY  7.757700  1.977413       5.067932       1.291800  3.776133   \n", "2    GIDLY  4.960080  0.854291       3.449043       0.594040  2.855002   \n", "3    TSGMY  6.152918  2.136821       4.019563       1.395937  2.623626   \n", "4    TNMPY  3.694215  0.758264       3.260248       0.669190  2.591058   \n", "..     ...       ...       ...            ...            ...       ...   \n", "301  FKDNY       NaN       NaN            NaN            NaN       NaN   \n", "302  FADNY       NaN       NaN            NaN            NaN       NaN   \n", "303  TRTNY       NaN       NaN            NaN            NaN       NaN   \n", "304  FRFPY       NaN       NaN            NaN            NaN       NaN   \n", "305  FCFLY       NaN       NaN            NaN            NaN       NaN   \n", "\n", "       round     yield1     yield2  \n", "0    initial  63.689822  10.023846  \n", "1    initial  70.447102  17.547888  \n", "2    initial  44.798116   7.062533  \n", "3    initial  55.734223  19.036107  \n", "4    initial  33.192484   6.166034  \n", "..       ...        ...        ...  \n", "301  round 1        NaN        NaN  \n", "302  round 1        NaN        NaN  \n", "303  round 1        NaN        NaN  \n", "304  round 1        NaN        NaN  \n", "305  round 1        NaN        NaN  \n", "\n", "[306 rows x 9 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#combine them together\n", "combined = pd.merge(selected, training_df, on='Combo', how='left')\n", "combined\n", "#replace round with round1 if NaN\n", "combined['round'] = combined['round'].fillna('round 1')\n", "combined.to_csv('data/ParPgb/predicted_variants.csv', index=False)\n", "combined"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>NormIso1</th>\n", "      <th>NormIso2</th>\n", "      <th>PlateNormIso1</th>\n", "      <th>PlateNormIso2</th>\n", "      <th>Diff</th>\n", "      <th>round</th>\n", "      <th>yield1</th>\n", "      <th>yield2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>216</th>\n", "      <td>FKTNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>217</th>\n", "      <td>FSFNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>218</th>\n", "      <td>FAFNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>FIFNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>FADYY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>301</th>\n", "      <td>FKDNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>302</th>\n", "      <td>FADNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>TRTNY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td>FRFPY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>305</th>\n", "      <td>FCFLY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>round 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>90 rows × 9 columns</p>\n", "</div>"], "text/plain": ["     Combo  NormIso1  NormIso2  PlateNormIso1  PlateNormIso2  Diff    round  \\\n", "216  FKTNY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "217  FSFNY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "218  FAFNY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "219  FIFNY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "220  FADYY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "..     ...       ...       ...            ...            ...   ...      ...   \n", "301  FKDNY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "302  FADNY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "303  TRTNY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "304  FRFPY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "305  FCFLY       NaN       NaN            NaN            NaN   NaN  round 1   \n", "\n", "     yield1  yield2  \n", "216     NaN     NaN  \n", "217     NaN     NaN  \n", "218     NaN     NaN  \n", "219     NaN     NaN  \n", "220     NaN     NaN  \n", "..      ...     ...  \n", "301     NaN     NaN  \n", "302     NaN     NaN  \n", "303     NaN     NaN  \n", "304     NaN     NaN  \n", "305     NaN     NaN  \n", "\n", "[90 rows x 9 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["#view only the predicted variants (there are 90 of them)\n", "combined[combined['round'] == 'round 1']"]}], "metadata": {"kernelspec": {"display_name": "dkbo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 2}